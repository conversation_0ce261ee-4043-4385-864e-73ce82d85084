<template>
    <view>
        <image :src="url" style="width: 100vw;height: 100vh;" />
    </view>
</template>
<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';

const loading = ref(false)
const url = ref('')
onLoad(async (o) => {
    if (o && o.imgId) {
        await init(o.imgId)
    }
});

const init = async (id: Number) => {
    loading.value = true
    const res = await uni.$u.http.get('/system/workConfig/' + id)
    loading.value = false
    if (res.code === 200) {
        const data = res.data
        url.value = data.url
    }
}
</script>
<style lang="sass" scoped>
</style>