{
  "extends": "@vue/tsconfig/tsconfig.json",
  "compilerOptions": {
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ]
    },
    "lib": [
      "esnext",
      "dom"
    ],
    "types": [
      "@dcloudio/types",
      "miniprogram-api-typings", // 原生微信小程序类型
      "uview-plus/types",
      // "@uni-helper/uni-app-types" // uni-app 组件类型
    ],
    "ignoreDeprecations": "5.0" //vue3 ts 配置目前时基于 webpack 4 的(版本低了) Option 'importsNotUsedAsValues' is deprecated and will stop functioning in TypeScript 5.5
  },
  "vueCompilerOptions": {
    "nativeTags": [
      "block",
      "component",
      "template",
      "slot"
    ],
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ]
}