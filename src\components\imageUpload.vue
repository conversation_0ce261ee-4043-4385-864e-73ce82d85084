<template>
    <view>
        <up-upload
            :fileList="fileList"
            @afterRead="afterRead"
            @delete="deletePic"
            name="file"
            :multiple="mutiple"
            :accept="accept"
            :maxCount="maxCount"
        />
    </view>
</template>

<script setup lang="ts" >
import type { File } from '@/types/file';
import { reactive, watch } from 'vue';

const fileList = reactive<File[]>([])

const emit = defineEmits(['update:modelValue'])
const props = defineProps<{
    accept?: {
      type: string,
      default: 'image',
    },
    mutiple?: {
      type: boolean,
      default: false,
    },
    maxCount?: {
      type: number,
      default: 52,
    },
    modelValue: string,
}>()

watch(props,(v)=>{
  if(v && v.modelValue) {
    const array = v.modelValue.split(',').map(url => {
      return {
        url
      }
    })
    fileList.length = 0
    fileList.push(...array)
  }
},{immediate: true})

const deletePic = (event:any) => {
    fileList.splice(event.index, 1);
}

const afterRead = async (event: any) => {
  let lists = [].concat(event.file);
	uni.showLoading({
		title: '正在上传'
	});
  for(const item of lists) {
    const res = await uni.$u.http.upload('/common/upload',{filePath: item.url,name: 'file'})
    if(res.code === 200) {
      fileList.push({url: res.url})
    }
  }
  emit('update:modelValue',fileList.map(i=> i.url).join(','))
  uni.hideLoading()
};

</script>