<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { useAuthStore } from "@/state/modules/user";

const authStore = useAuthStore();
const updateManager = uni.getUpdateManager();
onLaunch(async (o) => {
  let loginRes = await uni.login();
  if (loginRes?.code) {
    let res = await uni.$u.http.get("/mp/weixin/jscode2session", {
      params: { code: loginRes.code },
    });
    if (res.code === 200) {
      authStore.wxToken = res.msg;
      let query = o?.query;
      if (query && query.scene) {
        let scene = query.scene;
        const res = await uni.$u.http.put("/share/userShare/" + scene);
      }
    }
  }

  updateManager.onCheckForUpdate(function (updateRes) {
    if (updateRes.hasUpdate) {
      updateManager.onUpdateReady(function (res) {
        uni.showModal({
            title: '更新提示',
            content: '新版本已经准备好，是否重启应用？',
            success(res) {
              if (res.confirm) {
                updateManager.applyUpdate();
              }
            }
        });

        updateManager.onUpdateFailed(function (res) {
          uni.showModal({
            title: '更新提示',
            content: '新版本下载失败，请检查网络！',
            showCancel: false,
            success(res) {
              if (res.confirm) {
                updateManager.applyUpdate();
              }
            }
          });
        });
      });
    }
  });

});
onShow(() => {});
onHide(() => {});

</script>
<style lang="scss" >
@import "uview-plus/index.scss";
@import "@/style/app.scss";
@import url("@/static/iconfont.css");
</style>
