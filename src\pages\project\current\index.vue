<template>
    <view class="page-wrapper">
		
        <view v-show="showSearch" class="form-wrapper" >
            <uni-forms :modelValue="queryParams">
                <uni-forms-item label="客户名称" name="customerName">
                    <uni-easyinput trim="both" v-model="queryParams.customerName" />
                </uni-forms-item>
                <uni-forms-item label="项目编号" name="projectNo">
                    <uni-easyinput trim="both" v-model="queryParams.projectNo" />
                </uni-forms-item>
                <uni-forms-item label="产品名" name="productName">
                    <uni-easyinput trim="both" v-model="queryParams.productName" />
                </uni-forms-item>
            </uni-forms>
            <button @click="handleQuery" size="mini" >查询</button>
            <button @click="resetQuery" size="mini" >重置</button>
        </view>

        <view class="table-tools">
            <view>
                <text class="ali-icon ali-tianjia" />
            </view>
            <view class="btn-wrapper">
                <uni-icons type="search" @click="showSearch =! showSearch" />
                <button
                    :disabled="btnLoading"
                    :loading="btnLoading"
                    @click="handleQuery"
                    size="mini"
                    plain
                >
                    <uni-icons type="reload" />
                </button>
            </view>
        </view>

        <uni-table border stripe emptyText="暂无更多数据" >
            <uni-tr>
                <uni-th width="80" align="center" >项目编号</uni-th>
                <uni-th>VIP项目</uni-th>
                <uni-th>客户名称</uni-th>
                <uni-th>客户级别</uni-th>
                <uni-th>项目状态</uni-th>
                <uni-th>产品名称</uni-th>
                <uni-th>品牌名</uni-th>
                <uni-th>客户担当</uni-th>
                <uni-th>申请人</uni-th>
                <!-- <uni-th width="50" align="center" >操作</uni-th> -->
            </uni-tr>
            <uni-tr v-for="item in dataArray" :key="item.id" >
                <uni-td>{{ item.projectNo }}</uni-td>
                <uni-td>{{ item.isVip }}</uni-td>
                <uni-td>{{ item.customerName }}</uni-td>
                <uni-td>{{ item.customerLevel }}</uni-td>
                <uni-td>{{ getDictLabel('project_status',item.status) }}</uni-td>
                <uni-td>{{ item.productName }}</uni-td>
                <uni-td>{{ item.brandName }}</uni-td>
                <uni-td>{{ item.customerAssist }}</uni-td>
                <uni-td>{{ item.createBy }}</uni-td>
                <!-- <uni-td>
                    <text class="ali-icon ali-bianji" />
                    <uni-icons type="list" @click="" />
                </uni-td> -->
            </uni-tr>
        </uni-table>

        <uni-pagination
            :current="queryParams.pageNum"
            :total="total"
            :page-size="queryParams.pageSize"
            @change="pageChange"
        />

		<up-toast ref="uToastRef" />
        <up-loading-page :loading="loading" />
	</view>
</template>
<script lang="ts" setup>
import type { Project } from '@/types/project/project';
import { getDictLabel, navTo, } from '@/utils/tools';
import { onLoad, onPullDownRefresh } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';

const queryParams = reactive<any>({
    pageNum: 1,
	pageSize: 10,
    customerName: '',
    projectNo: '',
    productName: '',
})
let loadmoreStatus = ref('loadmore')
const dataArray:Project[] = reactive([])
const loading = ref(false)
const btnLoading = ref(false)
const showSearch = ref(false)
const uToastRef = ref()
const total = ref<Number>(0)

onLoad(async()=> {
    await resetQuery()
})
onPullDownRefresh(async () => {
  await resetQuery();
  uni.stopPullDownRefresh();
});

const pageChange = async (e:Object) => {
    queryParams.pageNum = e.current
    await getList()
}
const handleQuery = async() => {
    dataArray.length = 0
    queryParams.pageNum = 1
    await getList()
}
const resetQuery = async () => {
    queryParams.customerName = ''
    queryParams.projectNo = ''
    queryParams.productName = ''
    await handleQuery()
}
const getList = async() => {
    const params = Object.assign({},queryParams)
    loadmoreStatus.value = 'loading'
    loading.value = true
    let projectRes = await uni.$u.http.get('/project/list',{params})
    loadmoreStatus.value = 'loadmore'
    loading.value = false
    dataArray.length = 0
    dataArray.push(...projectRes.rows)
    total.value = projectRes.total
    if (projectRes.length < queryParams.pageSize) {
        loadmoreStatus.value = 'nomore'
    }
}
</script>
<script lang="ts" >
export default {
    options: {
        styleIsolation: "shared"
    },
}
</script>
<style lang="scss" scoped>

::v-deep .uni-table {
    .uni-table-tr {
        .uni-table-th {
            background-color: rgba(248,248,249,1)
        }

        .uni-table-th:first-child,.uni-table-td:first-child {
            position: sticky;
            left: 0;
            background-color: rgba(248,248,249,1);
            z-index: 1; 
        }

        .uni-table-th:last-child,.uni-table-td:last-child {
            position: sticky;
            right: 0;
            background-color: rgba(248,248,249,1);
            z-index: 1; 
        }

    }
} 
</style>