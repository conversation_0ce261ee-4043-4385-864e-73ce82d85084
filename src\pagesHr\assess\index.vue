<template>
    <view class="page-wrapper">
		
        <view v-show="showSearch" class="form-wrapper" >
            <uni-forms :modelValue="queryParams">
                <uni-forms-item label="姓名" name="nickName">
                    <uni-easyinput trim="both" v-model="queryParams.nickName" />
                </uni-forms-item>
            </uni-forms>
            <button @click="handleQuery" size="mini" >查询</button>
            <button @click="resetQuery" size="mini" >重置</button>
        </view>

        <view class="table-tools">
            <view>
            </view>
            <view class="btn-wrapper">
                <uni-icons type="search" @click="showSearch =! showSearch" />
                <button
                    :disabled="btnLoading"
                    :loading="btnLoading"
                    @click="handleQuery"
                    size="mini"
                    plain
                >
                    <uni-icons type="reload" />
                </button>
            </view>
        </view>

        <uni-table border stripe emptyText="暂无更多数据" >
            <uni-tr>
                <uni-th width="120" align="center" >考核日期</uni-th>
                <uni-th width="80" align="center" >姓名</uni-th>
                <uni-th align="center" >总分</uni-th>
                <uni-th align="center" >评价</uni-th>
                <uni-th align="center" >执行方式</uni-th>
                <uni-th width="50" align="center" >操作</uni-th>
            </uni-tr>
            <uni-tr v-for="item in dataArray" :key="item.id" >
                <uni-td >{{ item.assessDate }}</uni-td>
                <uni-td >{{ item.nickName }}</uni-td>
                <uni-td >{{ item.score }}</uni-td>
                <uni-td >{{ item.appraise }}</uni-td>
                <uni-td >{{ item.execute }}</uni-td>
                <uni-td >
                    <text class="ali-icon ali-bianji" @click="navTo('/pagesHr/assess/save?id=' + item.id)" />
                </uni-td>
            </uni-tr>
        </uni-table>

        <uni-pagination
            :current="queryParams.pageNum"
            :total="total"
            :page-size="queryParams.pageSize"
            @change="pageChange"
        />

		<up-toast ref="uToastRef" />
        <up-loading-page :loading="loading" />
	</view>
</template>
<script lang="ts" setup>
import type { UserAssessItem } from '@/types/hr/userAssessItem';
import { getDictLabel, navTo,getDictData, } from '@/utils/tools';
import { onLoad, onUnload, onPullDownRefresh } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';

const queryParams = reactive<any>({
    pageNum: 1,
	pageSize: 10,
    nickName: '',
})
let loadmoreStatus = ref('loadmore')
const dataArray:UserAssessItem[] = reactive([])
const loading = ref(false)
const btnLoading = ref(false)
const showSearch = ref(false)
const uToastRef = ref()
const total = ref<Number>(0)

onLoad(async()=> {
    uni.$on('refreshUserAssessItem',getList)
    await resetQuery()
})
onUnload(async() => {
    uni.$off('refreshUserAssessItem',getList)
})
onPullDownRefresh(async () => {
  await resetQuery();
  uni.stopPullDownRefresh();
});

const pageChange = async (e:Object) => {
    queryParams.pageNum = e.current
    await getList()
}
const handleQuery = async() => {
    dataArray.length = 0
    queryParams.pageNum = 1
    await getList()
}
const resetQuery = async () => {
    queryParams.nickName = ''
    await handleQuery()
}
const getList = async() => {
    const params = Object.assign({},queryParams)
    loadmoreStatus.value = 'loading'
    loading.value = true
    let logRes = await uni.$u.http.get('/user/assess/item/todoList',{params})
    loadmoreStatus.value = 'loadmore'
    loading.value = false
    dataArray.length = 0
    dataArray.push(...logRes.rows)
    total.value = logRes.total
    if (logRes.length < queryParams.pageSize) {
        loadmoreStatus.value = 'nomore'
    }
}
</script>
<script lang="ts" >
export default {
    options: {
        styleIsolation: "shared"
    },
}
</script>
<style lang="scss" scoped>

::v-deep .uni-table {
    .uni-table-tr {
        .uni-table-th {
            background-color: rgba(248,248,249,1)
        }

        .uni-table-th:first-child,.uni-table-td:first-child {
            position: sticky;
            left: 0;
            background-color: rgba(248,248,249,1);
            z-index: 1; 
        }

        .uni-table-th:last-child,.uni-table-td:last-child {
            position: sticky;
            right: 0;
            background-color: rgba(248,248,249,1);
            z-index: 1; 
        }

    }
} 
</style>