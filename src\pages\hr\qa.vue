<template>
    <liu-slide-questions :dataList="list" @submit="subData" />

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>

<script lang="ts" setup>
import { reactive, ref, inject } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import {secondToMinute} from '@/utils/tools'

const dayjs: any = inject('dayjs')
const data: any[] =  [
    {"A":"富于冒险","B":"生动活泼","C":"善于分析","D":"适应性强"},
    {"A":"善于说服","B":"喜好娱乐","C":"坚持不懈","D":"平和"},
    {"A":"意志坚定","B":"善于社交","C":"自我牺牲","D":"较少争辩"},
    {"A":"喜竞争性","B":"使人认同","C":"体贴","D":"自控性好"},
    {"A":"善于应变","B":"使人振作","C":"尊重他人","D":"含蓄"},
    {"A":"自立","B":"生机勃勃","C":"敏感","D":"满足"},
    {"A":"积极","B":"推动者","C":"计划者","D":"耐性"},
    {"A":"肯定","B":"无拘无束","C":"时间性","D":"羞涩"},
    {"A":"乐观","B":"坦率","C":"井井有条","D":"迁就"},
    {"A":"强迫性","B":"有趣","C":"忠诚","D":"友善"},
    {"A":"勇敢","B":"可爱","C":"注意细节","D":"体贴"},
    {"A":"自信","B":"让人高兴","C":"文化修养","D":"贯彻始终"},
    {"A":"独立","B":"富激励性","C":"理想主义","D":"无攻击性"},
    {"A":"果断","B":"感情外露","C":"深沉","D":"淡然幽默"},
    {"A":"发起者","B":"喜交朋友","C":"音乐性","D":"调解者"},
    {"A":"执着","B":"多言","C":"考虑周到","D":"容忍"},
    {"A":"领导者","B":"活力充沛","C":"忠心","D":"聆听者"},
    {"A":"首领","B":"让人喜爱","C":"制图者","D":"知足"},
    {"A":"勤劳","B":"受欢迎","C":"完美主义者","D":"和气"},
    {"A":"无畏","B":"跳跃型","C":"规范型","D":"平衡"},
    {"A":"专横","B":"露骨","C":"乏味","D":"忸怩"},
    {"A":"缺同情心","B":"散漫","C":"不宽恕","D":"缺乏热情"},
    {"A":"逆反","B":"唠叨","C":"怨恨","D":"保留"},
    {"A":"率直","B":"健忘","C":"挑剔","D":"胆小"},
    {"A":"没耐性","B":"好插嘴","C":"优柔寡断","D":"无安全感"},
    {"A":"缺乏同情心","B":"难预测","C":"不受欢迎","D":"不参与"},
    {"A":"固执","B":"即兴","C":"难于取悦","D":"行动迟缓"},
    {"A":"自负","B":"放任","C":"悲观","D":"平淡"},
    {"A":"好争吵","B":"易怒","C":"孤芳自赏","D":"无目标"},
    {"A":"鲁莽","B":"天真","C":"消极","D":"腼腆"},
    {"A":"工作狂","B":"喜获认同","C":"不擅交际","D":"担忧"},
    {"A":"不圆滑老练","B":"喋喋不休","C":"过分敏感","D":"胆怯"},
    {"A":"跋扈","B":"生活紊乱","C":"抑郁","D":"腼腆"},
    {"A":"不容忍","B":"缺乏毅力","C":"内向","D":"无异议"},
    {"A":"喜操纵","B":"凌乱","C":"情绪化","D":"喃喃自语"},
    {"A":"顽固","B":"好表现","C":"有戒心","D":"缓慢"},
    {"A":"统治欲","B":"大嗓门","C":"孤僻","D":"懒惰"},
    {"A":"易怒","B":"不专注","C":"多疑","D":"拖延"},
    {"A":"轻率","B":"烦躁","C":"报复型","D":"勉强"},
    {"A":"狡猾","B":"善变","C":"好批评","D":"妥协"},
]
const list:any[] = reactive([])
const startTime = ref('')
const loading = ref(false)
const uToastRef = ref()

onLoad(async (o:any)=> {
    list.length = 0
    for(let i=0;i<data.length;i++) {
        const o = data[i]
        list.push({
            id: (i + 1) + '',
            title: '单选',
            problemType: 'SINGLE',
            children: [
                {alias: 'A',answer: o.A,isSelect: 0 },
                {alias: 'B',answer: o.B,isSelect: 0 },
                {alias: 'C',answer: o.C,isSelect: 0 },
                {alias: 'D',answer: o.D,isSelect: 0 },
            ],
        })
    }
    startTime.value = dayjs().format("YYYY-MM-DD HH:mm:ss")
})

const subData = async (data:any[]) => {
    const answerArray = []
    const array = JSON.parse(JSON.stringify(data))
    for (const key in array) {
        answerArray.push({
            questionId: array[key].id,
            alias: array[key].userAnswer.alias,
            answer: array[key].userAnswer.answer,
        })
    }

    const optionArray = [
        {key: 'A',value: 0},
        {key: 'B',value: 0},
        {key: 'C',value: 0},
        {key: 'D',value: 0},
    ]

    for(const o of optionArray) {
        o.value = answerArray.filter(i=>i.alias === o.key).length
    }
    let discResult = optionArray.map(i=>i.key + i.value).join('')
    const maxKey = optionArray.sort((a,b)=> b.value-a.value)[0].key
    let type = ''
    switch(maxKey) {
        case 'A': type='掌控型'; break;
        case 'B': type='影响型'; break;
        case 'C': type='严谨性'; break;
        case 'D': type='沉稳型'; break;
    }

    discResult = discResult + '(' + type + ')'
    const params = {
        startTime: startTime.value,
        endTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
        answerArray: JSON.stringify(answerArray),
        discResult,
        discSecond: 0,
    }
    params.discSecond = dayjs(params.endTime).diff(params.startTime,'second')
    params.discResult = params.discResult + '用时:' + secondToMinute(params.discSecond)

    try {
        let res = await uni.$u.http.put('/entry/user',params)
        if(res.code === 200) {
            uToastRef.value.show({
				type: 'success',
				message: '提交成功,请等候通知!',
				complete() {
                    uni.reLaunch({url: '/pages/index/index'})
				}
			})
        } else {
            uToastRef.value.show({
				type: 'error',
				message: res.msg,
			})
        }
	} catch (error) {
    }
}
</script>