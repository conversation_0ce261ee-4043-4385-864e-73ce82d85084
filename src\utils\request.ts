import {http} from 'uview-plus'

const initRequest=()=>{
	
	http.setConfig((defaultConfig: { baseURL: any; header: { 'content-type': string } }) => {
		defaultConfig.baseURL = import.meta.env.VITE_BASE_URL
		defaultConfig.header = {
			'content-type': "application/json"
		}
		return defaultConfig
	})

	http.interceptors.request.use((config: { data: {}; }) => { 
		config.data = config.data || {}
		const authStr = uni.getStorageSync('auth');
		if(authStr) {
			const auth = JSON.parse(authStr)
			if(auth?.token) {
				config.header.Authorization = 'EnowRequestBearer ' + auth.token
			}
			if(auth?.wxToken) {
				config.header.WxAuth = 'WxBearer ' + auth.wxToken
			}
	    }
		return config
	}, (config: any) => Promise.reject(config))

	http.interceptors.response.use((response: { data: any; config: { custom: any; }; }) => { 
		const res = response.data

		if(res.code) { // 如果没有 code, 一般是返回的是数组
			if(res.code === 401) {
				uni.$u.route('/pages/login/index')
			}
		}
		return res
	}, (response: any) => {
		return Promise.reject(response)
	})
}

export {
	initRequest
}