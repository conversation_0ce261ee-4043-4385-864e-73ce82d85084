<template>
	<view class="">
		<up-icon @click="addData" name="plus-circle-fill" color="#2979ff" size="28"></up-icon>
	</view>
	<view>
		<uni-collapse accordion ref="collapse" v-model="collapseValue">
			<uni-collapse-item titleBorder="none" v-for="(item, i) in rewardData" :key="item.key">
				<template v-slot:title>
					<uni-row class="demo-uni-row">
						<uni-col span="18">
							<view><uni-tag :mark="true" :text="item.title" type="primary" size="default" /></view>
						</uni-col>
						<uni-col span="6" class="uni-text-right">
							<up-icon @click="removeData(item)" name="minus-circle-fill" color="#ff0000"
								size="28"></up-icon>
						</uni-col>
					</uni-row>
				</template>
				<view class="content">
					<uni-forms ref="baseForm" :modelValue="item.form" label-width="80px">
						<uni-section class="mb-10" title="考核对象" type="line" sub-title=""></uni-section>
						<uni-forms-item label="考核对象" required>
							<input v-model="keyword" placeholder="输入关键词搜索" @input="filterData" />
							<uni-data-picker ellipsis mode="multiSelector" placeholder="请选择考核对象" popup-title="请选择"
								field="text" :localdata="classDataTree" v-model="item.form.userId"
								:map="{ text: 'name', value: 'id' }" @change="pickerChange($event, item)"
								@nodeclick="pickerClick" @inputclick="inputclick"></uni-data-picker>
						</uni-forms-item>
						<uni-section class="mb-10" title="组织贡献" type="line" sub-title=""></uni-section>
						<uni-forms-item label="加分分值" required>
							<uni-number-box v-model="item.form.rewardJson.value" @blur="blur" @focus="focus"
								@change="changeValue" />
						</uni-forms-item>
						<uni-forms-item label="贡献描述" required>
							<uni-easyinput v-model="item.form.rewardJson.key" placeholder="请输入贡献描述" />
						</uni-forms-item>

						<uni-section class="mb-10" title="投诉或日常工作质量" type="line" sub-title=""></uni-section>
						<uni-forms-item label="减分分值" required>
							<uni-number-box v-model="item.form.punishJson.value" @blur="blur" @focus="focus"
								@change="changeValue" />
						</uni-forms-item>
						<uni-forms-item label="异常描述" required>
							<uni-easyinput v-model="item.form.punishJson.key" placeholder="请输入异常描述" />
						</uni-forms-item>

						<uni-section class="mb-10" title="附件" type="line" sub-title=""></uni-section>
						<uni-forms-item label="附件">
							<FileUpload v-model="item.form.fileJson" :resize="collapseResize" :fileMediatype="'all'"
								:limit="5" />
						</uni-forms-item>

					</uni-forms>
				</view>
			</uni-collapse-item>
		</uni-collapse>

		<view class="">
			<button type="primary" :loading="butLoading" @click="submit">提 交</button>
		</view>
	</view>



</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app';
import FileUpload from '@/components/fileUpload.vue'
const collapse = ref(null);
const butLoading = ref(false)
const index = ref<number>(0)
const rewardData = ref([{ title: "考核对象", form: { rewardJson: {}, punishJson: {}, fileJson: [] }, key: index }])
const collapseValue = ref('0')
const form = reactive({})
const classDataTree = ref([]);

onLoad(async () => {
	console.log("初始化数据")

	let res = await uni.$u.http.get("/hr/reward/deptUserTree1", {
		params: { deptId: 0 },
	});
	getUser(res) // 递归获取用户
	classDataTree.value = res
	console.log("classDataTree", classDataTree.value)
})

const getUser = async (aData: object[]) => {
	aData.forEach((item: any) => {
		if (item.userList && item.userList.length > 0) {
			item.userList.forEach((user: any) => {
				item.children?.unshift({ id: user.userId, name: user.nickName }) // 用户作为子节点
			})
		}
		if (item.children && item.children.length > 0) {
			getUser(item.children)
		}
	})

}
const pickerClick = async (e: any) => {
	// console.log('nodeclick', e);
}
const pickerChange = async (e, item) => {
	console.log('onchange', e);
	let value = e.detail.value
	item.title = value[value.length - 1].text
	item.form.deptId = value[value.length - 2].value
	item.form.userId = value[value.length - 1].value
}
const addData = async (e: any) => {
	rewardData.value.push({ title: "考核对象", form: { rewardJson: {}, punishJson: {}, fileJson: [] }, key: index.value++ })
}
const removeData = async (item: any) => {
	const index = rewardData.value.findIndex((i) => i.key === item.key)
	if (index !== -1) {
		rewardData.value.splice(index, 1)
	}
}

const submit = async () => {
	console.log("rewardData", rewardData)
	butLoading.value = true
}

const collapseResize = async () => {
	collapse.value.resize();
	console.log(rewardData)
}

</script>

<style lang="scss" scoped>
.page-wrapper {
	padding: 20rpx 20rpx 0;

	::v-deep .uni-row {

		.item-wrapper {
			border: 2rpx $uni-border-1 solid;
			border-radius: 4px;
			height: 56rpx;
			line-height: 56rpx;
			color: $uni-primary;
			padding: 0 10rpx;
			margin-bottom: 10px;
			font-size: $font-sm;
		}
	}

}

.uni-forms-item {
	width: 100%;
	/* 或者具体的宽度 */
	height: auto;
	/* 或者具体的高度 */
	overflow: visible;
	/* 避免内容被裁剪 */


}

.example-body {
	flex-direction: column;
	flex: 1;
}

.content {
	padding: 15px;
}

.text {
	font-size: 14px;
	color: #666;
	line-height: 20px;
}

.button {
	// margin-top: 10px;
	margin: 10px;
	margin-bottom: 0;
}
</style>