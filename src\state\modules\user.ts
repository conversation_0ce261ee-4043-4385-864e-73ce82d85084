import type { GxUser } from '@/types/gx/gxUser'
import type { User } from '@/types/system/user'

import { defineStore } from 'pinia'

interface AuthState {
  wxToken?: string
  token?: string
  user: User,
  gxUser: GxUser,
}

export const useAuthStore = defineStore({
  id: 'auth',
  state: (): AuthState => ({
    wxToken: undefined,
    token: undefined,
    user: {} as User,
    gxUser: {} as GxUser,
  }),
  getters: {
    isLogin(): boolean {
      return this.token !== undefined
    },
  },
  actions: {
    async login(info: any) {
      const res = await uni.$u.http.post('/user/login',info)
      if(res.code === 200) {
        this.token = res.token
        this.user = res.user
      }
      return new Promise<any>((resolve,reject) => {
        resolve(res),reject(res)
      });
    },
    layout() {
      this.token = undefined
      this.user = {}
    },
  },
  // 本地持久化存储
  persist: {
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync,
    },
  },
})