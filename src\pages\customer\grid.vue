<template>
   <view>
    <uni-grid :column="2" >
      <uni-grid-item>
        <view class="grid-item" @click="navTo('')" >
            <text class="label">财务信息</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item" @click="navTo('')" >
            <text class="label">联系人</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item" @click="navTo('')" >
            <text class="label">委托加工合同</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item" @click="navTo('')" >
            <text class="label">订单</text>
        </view>
      </uni-grid-item>
    </uni-grid>
   </view>
</template>
<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';
import { navTo, } from '@/utils/tools';

const currentCustomerId = ref<Number>()
const currentCustomerName = ref<String>()

onLoad((o)=> {
    if(o.id) {
        currentCustomerId.value = o.id
    }
    if(o.name) {
        currentCustomerName.value = o.name
        uni.setNavigationBarTitle({
            title: o.name
        })
        
    }
})
</script>
<style lang="scss" scoped >
  .grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;

    .label {
      margin-top: 10rpx;
      font-size: $font-sm;
    }
  }
</style>