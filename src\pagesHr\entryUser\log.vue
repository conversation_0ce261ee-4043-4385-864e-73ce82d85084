<template>
    <view class="form-wrapper" >
        <uni-forms :modelValue="form" :rules="rules" ref="formRef" >
            <uni-forms-item label="姓名" name="userName" label-width="80px" >
                <uni-tag :inverted="true" :text="form.userName" type="primary" @click="navTo('/pages/hr/apply?userId=' + form.userId + '&readonly=' + true)" />
            </uni-forms-item>
            <uni-forms-item label="面试日期" required name="interViewDate" label-width="80px">
                <uni-datetime-picker type="date" v-model="form.interViewDate" @change="scoreChange" />
            </uni-forms-item>
            <uni-forms-item label="基本素质" required name="jbSz" label-width="80px">
                <uni-data-select v-model="form.jbSz" :localdata="scoreOptions" @change="scoreChange" />
            </uni-forms-item>
            <uni-forms-item label="责任意识" required name="zrYs" label-width="80px">
                <uni-data-select v-model="form.zrYs" :localdata="scoreOptions" @change="scoreChange" />
            </uni-forms-item>
            <uni-forms-item label="岗位知识" required name="gwZs" label-width="80px">
                <uni-data-select v-model="form.gwZs" :localdata="scoreOptions" @change="scoreChange" />
            </uni-forms-item>
            <uni-forms-item label="行业经历" required name="ysJl" label-width="80px">
                <uni-data-select v-model="form.ysJl" :localdata="scoreOptions" @change="scoreChange" />
            </uni-forms-item>
            <uni-forms-item label="领导能力" required name="ldNl" label-width="80px">
                <uni-data-select v-model="form.ldNl" :localdata="scoreOptions" @change="scoreChange" />
            </uni-forms-item>
            <uni-forms-item label="协作能力" required name="xzNl" label-width="80px">
                <uni-data-select v-model="form.xzNl" :localdata="scoreOptions" @change="scoreChange" />
            </uni-forms-item>
            <uni-forms-item label="职业规划" required name="gh" label-width="80px">
                <uni-data-select v-model="form.gh" :localdata="scoreOptions" @change="scoreChange" />
            </uni-forms-item>
            <uni-forms-item label="薪资要求" required name="xzYq" label-width="80px">
                <uni-data-select v-model="form.xzYq" :localdata="scoreOptions" @change="scoreChange" />
            </uni-forms-item>
            <uni-forms-item label="学习能力" required name="xxNl" label-width="80px">
                <uni-data-select v-model="form.xxNl" :localdata="scoreOptions" @change="scoreChange" />
            </uni-forms-item>
            <uni-forms-item label="情绪管理" required name="qxGl" label-width="80px">
                <uni-data-select v-model="form.qxGl" :localdata="scoreOptions" @change="scoreChange" />
            </uni-forms-item>
            <uni-forms-item label="评分" name="score" label-width="80px">
                <view style="display: flex;align-items: center;height: 100%;">
                    {{ form.score }} <text v-if="form.level" :style="levelStyle()" >({{form.level}})</text>
                </view>
            </uni-forms-item>
            <uni-forms-item label="评价" required name="appraise" label-width="80px">
                <uni-easyinput trim="both" v-model="form.appraise" type="textarea" />
            </uni-forms-item>
            <uni-forms-item label="结论" required name="result" label-width="80px">
                <uni-data-select v-model="form.result" :localdata="resultOptions" />
            </uni-forms-item>
        </uni-forms>
        <button v-if="!readonly" @click="submit" type="primary" :loading="btnLoading" :disabled="btnLoading" >提 交</button>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { EntryUserLog } from '@/types/hr/entryUserLog';
import { onLoad } from '@dcloudio/uni-app';
import { nextTick, reactive, ref } from 'vue';
import Big from 'big.js';
import { navTo } from '@/utils/tools';

const form = reactive<EntryUserLog>({
    id: undefined,
    jbSz: undefined,
    zrYs: undefined,
    gwZs: undefined,
    ysJl: undefined,
    ldNl: undefined,
    xzNl: undefined,
    gh: undefined,
    xzYq: undefined,
    xxNl: undefined,
    qxGl: undefined,
    score: undefined,
    level: undefined,
    result: '',
    appraise: '',
    operatorId: undefined,
    step: '',
    interViewDate: '',
});
const rules = reactive({
	interViewDate: {
		rules: [
			{required: true, errorMessage: '请选择面试日期',},
		],
	},
    jbSz: {
		rules: [
			{required: true, errorMessage: '请输入基本素质',},
		],
	},
    zrYs: {
		rules: [
			{required: true, errorMessage: '请输入责任意识',},
		],
	},
    gwZs: {
		rules: [
			{required: true, errorMessage: '请输入岗位知识',},
		],
	},
    ysJl: {
		rules: [
			{required: true, errorMessage: '请输入行业经历',},
		],
	},
    ldNl: {
		rules: [
			{required: true, errorMessage: '请输入领导能力',},
		],
	},
    xzNl: {
		rules: [
			{required: true, errorMessage: '请输入协作能力',},
		],
	},
    gh: {
		rules: [
			{required: true, errorMessage: '请输入职业规划',},
		],
	},
    xzYq: {
		rules: [
			{required: true, errorMessage: '请输入薪资要求',},
		],
	},
    xxNl: {
		rules: [
			{required: true, errorMessage: '请输入学习能力',},
		],
	},
    qxGl: {
		rules: [
			{required: true, errorMessage: '请输入情绪管理',},
		],
	},
    appraise: {
		rules: [
			{required: true, errorMessage: '请输入评价',},
		],
	},
    result: {
		rules: [
			{required: true, errorMessage: '请输入结论',},
		],
	},
})
const btnLoading = ref(false)
const loading = ref(false)
const formRef = ref()
const entryUserSaveRef = ref()
const uToastRef = ref()
const scoreOptions = [
    {text: '优秀+2',value: 2},
    {text: '良好+1',value: 1},
    {text: '普通0',value: 0},
    {text: '较差-1',value: -1},
    {text: '很差-2',value: -2},
]
const resultOptions = [
    {text: '待定',value: '1'},
    {text: '录用',value: '2'},
    {text: '不合格',value: '3'},
]
const readonly = ref(false)
const propArray = [
    'jbSz',
    'zrYs',
    'gwZs',
    'ysJl',
    'ldNl',
    'xzNl',
    'gh',
    'xzYq',
    'xxNl',
    'qxGl',
]

onLoad(async (o:any)=> {
    if(o.id) {
        await init(o.id)
    }
    if(o.readonly) {
        readonly.value = true
    } else {
        readonly.value = false
    }
})

const levelStyle = () => {
    const score = form.score
    let color = '';
    if(score !== undefined) {
        if(score >= 12 ) {
            color = '#409EFF'
        } else if (score < 12 && score >= 6) {
            color = '#67C23A'
        } else if (score < 6 && score >= 0) {
            color = '#E6A23C'
        } else if (score < 0 && score >= -6) {
            color = '#F56C6C'
        } else if (score < -6 && score >= -12) {
            color = '#909399'
        }
    }
    return {
        color: color,
    }
}
const scoreChange = () => {
    let score = Big(0)
    for (const prop of propArray) {
        if(form[prop]) {
            score = score.plus(form[prop])
        }
    }
    score = score.toNumber()
    form.score = score
    if(score >= 12 ) {
        form.level = 'S'
    } else if (score < 12 && score >= 6) {
        form.level = 'A'
    } else if (score < 6 && score >= 0) {
        form.level = 'B'
    } else if (score < 0 && score >= -6) {
        form.level = 'C'
    } else if (score < -6 && score >= -12) {
        form.level = 'D'
    }
}
const init = async (id: Number) => {
    loading.value = true
    const res = await uni.$u.http.get('/entry/user/log/' + id)
    loading.value = false
    if(res.code === 200) {
        const data = res.data
        Object.assign(form, data)

        await nextTick()
        if(entryUserSaveRef.value && entryUserSaveRef.value.init) {
            entryUserSaveRef.value.readonly = true
            entryUserSaveRef.value.init(form.userId)
        }
    }
}
const submit = async() => {
    const validateRes = await formRef.value.validate()
    const subForm = Object.assign({},form)
    let res;
    try {
        btnLoading.value = true
        if (subForm.id != null) {
            res = await uni.$u.http.put('/entry/user/log',subForm)
        }
        btnLoading.value = false
        if(res.code === 200) {
            uToastRef.value.show({
				type: 'success',
				message: '提交成功',
				complete() {
                    uni.$emit('refreshEntryUserLog')
					uni.navigateBack({
						delta: 1
					})
				}
			})
        } else {
            uToastRef.value.show({
				type: 'error',
				message: res.msg,
			})
        }
	} catch (error) {
        btnLoading.value = false
    }

}

</script>
<style lang="scss" scoped>
</style>