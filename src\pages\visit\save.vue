<template>
    <view class="page-wrapper" >
        <uni-title type="h4" title="申请人" />
        <view >
            <text>
                {{ form.createBy }}
            </text>
        </view>
        <uni-title type="h4" title="客户名称" />
        <view >
            <text>
                {{ form.name }}
            </text>
        </view>
        <uni-title type="h4" title="公司名称" />
        <view >
            <text>
                {{ form.companyName }}
            </text>
        </view>
        <uni-title type="h4" title="访问事由" />
        <view >
            <text>
                {{ form.purpose }}
            </text>
        </view>
        <uni-title type="h4" title="预约访问时间" />
        <view >
            <text>
                {{ form.scheduleTime }}
            </text>
        </view>
        <uni-title type="h4" title="来访人数" />
        <view >
            <uni-easyinput trim="both" v-model="form.companionCount" />
        </view>
    </view>
    <view class="bottom-wrapper">
        <button
            :loading="btnLoading"
            :disabled="btnLoading"
            class="success"
            @click="confirm()"

        >
            确认
        </button>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { CustomerVisit } from '@/types/customer/customerVisit';
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';

const btnLoading = ref(false)
const loading = ref(false)
const uToastRef = ref()
const form = reactive<CustomerVisit>({
    id: undefined,
    userId: undefined,
    createBy: '',
    name: '',
    phone: '',
    companionCount: undefined,
    companyName: '',
    purpose: '',
    scheduleTime: '',
    checkinTime: '',
    checkoutTime: '',
});

onLoad(async (o:any)=> {
    await init(o.id)
})

const init = async(id: number) => {
    loading.value = true
    const res = await uni.$u.http.get('/customer/visit/' + id)
    loading.value = false
    if(res.code === 200) {
        const data = res.data
        Object.assign(form, data)
    }
}

const confirm = async () => {
    const params = {
        id: form.id,
        companionCount: form.companionCount,
    }
    try {
        btnLoading.value = true
        const res = await uni.$u.http.put('/customer/visit/arrived',params)
        btnLoading.value = false
        if(res.code === 200) {
            uToastRef.value.show({
				type: 'success',
				message: '提交成功',
				complete() {
					uni.reLaunch({
                        url: '/pages/index/index'
                    })
				}
			})
        } else {
            uToastRef.value.show({
				type: 'error',
				message: res.msg,
			})
        }
	} catch (error) {
        btnLoading.value = false
    }
}
</script>
<style lang="scss" scoped>
.bottom-wrapper {
    position: absolute;
    bottom: 0;
    width: 100%;
    border-radius: 0;
}

.success {
    color: #fff;
    background-color: $uni-success;
}

.error {
    color: #fff;
    background-color: $uni-error;
}

</style>