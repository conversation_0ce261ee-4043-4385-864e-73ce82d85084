<template>
    <view class="list-wrapper">

        <uni-segmented-control 
            :current="current" 
            :values="tabs" 
            @clickItem="tabChange" 
        />
        
        <view style="height: 20rpx;" />

        <view class="item-wrapper" v-for="item in dataArray" :key="item.id" >
            <view class="img-wrapper">
                <image
                    style="width: 200rpx;"
                    :src="item.img"
                    mode="widthFix"
                />
            </view>
            <view class="content-wrapper">
                <view class="title" >
                    <uni-tag :inverted="true" type="success" :text="item.typeTreeLabel" size="small" />
                    {{ item.title }}
                </view>
                <view class="date" >{{ item.dkDate }}</view>
                <view class="time" >
                    <text>
                        {{ item.startTime }} - {{ item.endTime }} {{ '&nbsp;' }}
                    </text>
                </view>
            </view>
            <view class="op-wrapper">
                <button type="primary" v-if="current === 1 && !item.dkTime" size="mini" @click="navTo('/pagesGx/case/user/dk/save?caseUserDkId=' + item.id)" >打卡</button>
            </view>
        </view>
	</view>

    <up-toast ref="uToastRef" />
    <up-loading-page :loading="loading" />
</template>
<script lang="ts" setup>
import type { GxCaseUserDk } from '@/types/gx/gxCaseUserDk';
import {navTo, getDictLabel } from '@/utils/tools';
import { onLoad, onUnload, onPullDownRefresh } from '@dcloudio/uni-app';
import { reactive, ref, inject } from 'vue';
import { useAuthStore } from '@/state/modules/user';

const dayjs: any = inject('dayjs')
const authStore = useAuthStore();
const tabs = ['未开始','进行中','已结束','已完成','未打卡']
const current = ref(1)
const queryParams = reactive<any>({
    pageNum: 1,
	pageSize: 10,
    title: null,
})
let loadmoreStatus = ref('loadmore')
const dataArray:GxCaseUserDk[] = reactive([])
const loading = ref(false)
const btnLoading = ref(false)
const showSearch = ref(false)
const uToastRef = ref()
const total = ref<Number>(0)

onLoad(async()=> {
    uni.$on('refreshGxCaseUserDkList', getList);
    await resetQuery()
})
onUnload(async() => {
    uni.$off('refreshGxCaseUserDkList')
})
onPullDownRefresh(async () => {
  await resetQuery();
  uni.stopPullDownRefresh();
});

const tabChange = async(e:any) => {
    current.value = e.currentIndex
    await getList()
}
const pageChange = async (e:any) => {
    queryParams.pageNum = e.current
    await getList()
}
const handleQuery = async() => {
    dataArray.length = 0
    queryParams.pageNum = 1
    await getList()
}
const resetQuery = async () => {
    queryParams.title = ''
    await handleQuery()
}
const getList = async() => {
    const userId = authStore.gxUser.id
    if(userId) {
        const params = Object.assign({},queryParams)
        params.userId = userId
        params.state = current.value
        params.status = '2' //已通过的
        loadmoreStatus.value = 'loading'
        loading.value = true
        let logRes = await uni.$u.http.get('/gx/caseUserDk/list',{params})
        loadmoreStatus.value = 'loadmore'
        loading.value = false
        dataArray.length = 0
        dataArray.push(...logRes.rows)
        total.value = logRes.total
        if (logRes.length < queryParams.pageSize) {
            loadmoreStatus.value = 'nomore'
        }
    }
}
</script>
<script lang="ts" >
export default {
    options: {
        styleIsolation: "shared"
    },
}
</script>
<style lang="scss" >
page {
    background-color: rgba(241, 241, 241,1)
}
</style>
<style lang="scss" scoped>
.list-wrapper {
    margin: 20rpx;

    .item-wrapper {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;
        border: $uni-border-1;
        border-radius: 20rpx;
        background-color: #fff;

        .img-wrapper {
            flex: 3;
        }

        .content-wrapper {
            flex: 6;

            .title {
                font-size: 28rpx;
                color: #3b4144;
            }

            .date {
                margin-top: 3px;
                color: #999;
                font-size: 24rpx;
                overflow: hidden;
            }

            .time {
                margin-top: 2px;
                color: #999;
                font-size: 18rpx;
                overflow: hidden;
            }
        }

        .op-wrapper {
            flex: 3;
        }

    }

}
</style>