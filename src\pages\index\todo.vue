<template>
    <view class="page-wrapper" >
        <uni-row :gutter="20" >
            <uni-col :span="12">
                <view class="item-wrapper" @click="navTo('/pagesHr/entryUser/index')" >
                    <uni-badge :text="badge.userLogToDoCount" absolute="rightTop" size="small" >
                        <text>招聘面试反馈</text>
                    </uni-badge>
                </view>
            </uni-col>
            <uni-col :span="12">
                <view class="item-wrapper" @click="navTo('/pagesHr/assess/index')" >
                    <uni-badge :text="badge.userAssessItemToDoCount" absolute="rightTop" size="small" >
                        <text>试用期考核反馈</text>
                    </uni-badge>
                </view>
            </uni-col>
        </uni-row>
        <uni-row :gutter="20" >
            <uni-col :span="12">
                <view class="item-wrapper" @click="navTo('/pagesSoftware/sampleOrder')" >
                    <uni-badge :text="badge.userLogToDoCount" absolute="rightTop" size="small" >
                        <text>打样单</text>
                    </uni-badge>
                </view>
            </uni-col>
        </uni-row>
    </view>
</template>
<script setup lang="ts">
import { navTo } from '@/utils/tools';
import { ref } from 'vue';
import { onLoad,onPullDownRefresh } from '@dcloudio/uni-app';

const badge = ref({
    userAssessItemToDoCount: null,
    userLogToDoCount: null,
    sumCount: null,
})

onLoad(async () => {
  await init();
})
onPullDownRefresh(async () => {
  await init();
  uni.stopPullDownRefresh();
});

const init = async() => {
  let badgeObj = await uni.$u.http.get('/index/badge')
  if(badgeObj) {
    badge.value = badgeObj
  }
}
</script>
<style lang="scss" scoped >
.page-wrapper {
    padding: 20rpx 20rpx 0;

    ::v-deep .uni-row {
  
        .item-wrapper {
            border: 2rpx $uni-border-1 solid;
            border-radius: 4px;
            height: 56rpx;
            line-height: 56rpx;
            color: $uni-primary;
            padding: 0 10rpx;
            margin-bottom: 10px;
            font-size: $font-sm;
        }
    }

}
</style>