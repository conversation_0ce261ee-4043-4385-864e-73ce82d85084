<template>
    <view class="form-wrapper" >

        <uni-forms :modelValue="form" :rules="rules" ref="formRef" label-position="top" >
            <uni-forms-item label="发送人" name="sendUser">
                {{ form.courseTitle }}
            </uni-forms-item>
            <uni-forms-item label="发送时间" name="sendTime">
                {{ form.teacher }}
            </uni-forms-item>

            <view v-for="(item,i) in formArray" :key="i" >
                <view v-if="existsFlag(formArray, item)" >
                    <view style="padding-bottom: 10rpx;" >{{ item.label }}</view>
                    <view style="padding-bottom: 10rpx;" >
                        <uni-easyinput v-if="item.type === 0" trim="both" v-model="item.value" />
                        <uni-data-checkbox v-if="item.type === 1" v-model="item.value" :localdata="item.options" />
                        <uni-data-checkbox v-if="item.type === 2" multiple v-model="item.value" :localdata="item.options" />
                    </view>
                </view>
            </view>

        </uni-forms>

        <button v-if="!readonly" 
            @click="submit"
            type="primary"
            :loading="btnLoading" 
            :disabled="btnLoading" >提 交</button>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { CourseFormUser } from '@/types/system/courseFormUser';
import { resetForm, } from '@/utils/tools';
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref, inject } from 'vue';

const form = reactive<CourseFormUser>({
    id: undefined,
    sendUser: '',
    sendTime: '',
    replyTime: '',
    duration: undefined,
    formArray: '',
    courseTitle: '',
    teacher: '',
});
const formArray:any[] = reactive([])
const rules = reactive({
})
const btnLoading = ref(false)
const loading = ref(false)
const formRef = ref()
const uToastRef = ref()
const readonly = ref(false)
const startTime = ref('')
const dayjs: any = inject('dayjs')

onLoad(async (o:any)=> {
    if(o.readonly) {
        readonly.value = true
    } else {
        readonly.value = false
    }
    resetForm(form)
    formArray.length = 0
    if(o.id) {
        loading.value = true
        let res = await uni.$u.http.get('/system/courseFormUser/' + o.id)
        if(res.code === 200 && res.data) {
            Object.assign(form,res.data)

            if(res.data.formArray) {
                const array = JSON.parse(res.data.formArray)
                formArray.length = 0
                for (const item of array) {
                    if([1,2].includes(item.type)) {
                        for (const sub of item.options) {
                            sub.text = sub.label
                            sub.value = sub.id
                        }
                    }
                    formArray.push(item)
                }
            }
        }
        loading.value = false
    }
    startTime.value = dayjs().format("YYYY-MM-DD HH:mm:ss")
})

const existsFlag = (formArray:any[], item:any) => {
    if(item.associationId) {
        const arr = formArray.filter(i=> i.id === item.associationId)
        if(arr && arr[0]) {
            if(arr[0].value === item.associationValue) {
                return true
            }
        } 
        return false
    } else {
        return true
    }
}
const submit = async() => {
    const validateRes = await formRef.value.validate()
    if(form.replyTime) {
        uToastRef.value.show({
            type: 'error',
            message: '该问卷已提交',
        })
        return
    }
    for (const item of formArray) {
        if(existsFlag(formArray, item)) {
            if(item.type==='1' && !item.value) {//如果是单选必填
                uToastRef.value.show({
                    type: 'error',
                    message: '请完整填写问卷',
                })
                return
            }
        }
    }
    const subForm:CourseFormUser = {
        id: form.id,
        formArray: JSON.stringify(formArray),
    }
    subForm.duration = dayjs(dayjs().format("YYYY-MM-DD HH:mm:ss")).diff(startTime.value,'second')

    try {
        btnLoading.value = true
        let res = await uni.$u.http.put('/system/courseFormUser',subForm)
        btnLoading.value = false
        if(res.code === 200) {
            uToastRef.value.show({
                type: 'success',
                message: '提交成功',
                complete() {
                    uni.reLaunch({url: '/pages/index/index'})
                }
            })
        } else {
            uToastRef.value.show({
                type: 'error',
                message: res.msg,
            })
        }
    } catch (error) {
        btnLoading.value = false
    }
}
</script>
<style lang="scss" scoped>

.bottom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
	background: rgba(41, 121, 255, 1);
    color: #fff;
    text-align: center;
    height: 50px;
}

</style>