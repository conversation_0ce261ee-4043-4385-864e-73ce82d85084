<template>
    <view class="form-wrapper" >

        <uni-title type="h2" :title="form.title" />

        <image
            v-if="form.img"
            :src="form.img"
            style="width: 710rpx"
            mode="'widthFix'"
        />

        <template v-if="form.startDate && form.endDate" >
            <uni-section title="打卡规则" type="line" />

            <uni-table border stripe emptyText="暂无更多数据" >
                <uni-tr>
                    <uni-th width="80" align="center" >打卡日期</uni-th>
                    <uni-td width="160" align="center" >
                        {{ form.startDate }} 至 {{ form.endDate }}
                    </uni-td>
                </uni-tr>
                <uni-tr>
                    <uni-th width="120" align="center" >打卡时间段</uni-th>
                    <uni-td width="160" align="center" >
                        <view v-for="(item,i) in dkTimeArray" :key="i" >
                            {{ item.startTime }} - {{ item.endTime }}
                        </view>
                    </uni-td>
                </uni-tr>
            </uni-table>
        </template>

        <template v-if="signDateArray.length" >
            <uni-section title="签到规则" type="line" />

            <uni-table border stripe emptyText="暂无更多数据" >
                <uni-tr>
                    <uni-th width="80" align="center" >签到日期</uni-th>
                    <uni-td width="160" align="center" >
                        {{ signDateArray.join(',') }}
                    </uni-td>
                </uni-tr>
                <uni-tr>
                    <uni-th width="120" align="center" >签到时间段</uni-th>
                    <uni-td width="160" align="center" >
                        <view v-for="(item,i) in signTimeArray" :key="i" >
                            {{ item.startTime }} - {{ item.endTime }}
                        </view>
                    </uni-td>
                </uni-tr>
            </uni-table>
        </template>

        <uni-section title="知情同意书" type="line" />

        <rich-text :nodes="form.zqText"></rich-text>

        <uni-section title="入选标准" type="line" />

        <uni-table border stripe emptyText="暂无更多数据" >
            <uni-tr v-for="(item,i) in includesArray" :key="i" >
                <uni-td align="center" >{{ item.label }}</uni-td>
                <uni-td align="center" >
                    <uni-data-select 
                        v-model="item.value" 
                        :localdata="whetherOptions" 
                        :placement="(i === (includesArray.length-1))?'top':'bottom'" 
                    />
                </uni-td>
            </uni-tr>
        </uni-table>

        <uni-section title="排除标准" type="line" />

        <uni-table border stripe emptyText="暂无更多数据" >
            <uni-tr v-for="(item,i) in excludesArray" :key="i" >
                <uni-td align="center" >{{ item.label }}</uni-td>
                <uni-td align="center" >
                    <uni-data-select 
                        v-model="item.value" 
                        :localdata="whetherOptions" 
                        :placement="(i === (excludesArray.length-1))?'top':'bottom'" 
                    />
                </uni-td>
            </uni-tr>
        </uni-table>

        <uni-section title="志愿者签字" type="line" />

        <l-signature ref="signatureRef" />

        <view class="btn-wrapper" v-if="!readonly && caseId" >
            <button type="warn" @click="clear" size="mini" >清空</button>
            <button @click="undo" size="mini" >上一步</button>
            <button 
                type="primary"
                size="mini"
                @click="showConfirm"
                :loading="btnLoading" 
                :disabled="btnLoading" >参与测试</button>
        </view>
    </view>

    <view>
        <uni-popup ref="submitConfirmRef" type="dialog">
            <uni-popup-dialog 
                title="注 意"
                @confirm="submit"
            >
                <template #default>
                    <text style="color: #e43d33;">测试期间请勿中断,是否确认?</text>
                </template>
            </uni-popup-dialog>
        </uni-popup>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { GxCase } from '@/types/gx/gxCase';
import { getDictData, navTo, resetForm, getDictLabel } from '@/utils/tools';
import { onLoad,onUnload } from '@dcloudio/uni-app';
import { reactive, ref, nextTick} from 'vue';
import { useAuthStore } from '@/state/modules/user';

const authStore = useAuthStore();
const form = reactive<GxCase>({
    id: undefined,
    title: '',
    startDate: '',
    endDate: '',
    remark: '',
    img: '',
    dkTimeArray: '',
    signDateArray: '',
    signTimeArray: '',
    zqText: '',
    includesArray: '',
    excludesArray: '',
});
const dkTimeArray:any[] = reactive([])
const signDateArray:any[] = reactive([])
const signTimeArray:any[] = reactive([])
const includesArray:any[] = reactive([])
const excludesArray:any[] = reactive([])
const btnLoading = ref(false)
const loading = ref(false)
const uToastRef = ref()
const submitConfirmRef = ref()
const readonly = ref(false)
const caseId = ref()
const signatureRef = ref()
const signature = ref('')
const whetherOptions = [
    {value: '0',text: '否'},
    {value: '1',text: '是'},
]

onLoad(async (o:any)=> {
    if(!uni.getStorageSync('dict_data')) {
        let dictRes = await uni.$u.http.get('/system/dict/all')
        uni.setStorageSync('dict_data',dictRes)
        uni.reLaunch({url: '/pagesGx/case/bind?caseId=' + o.caseId})
    }
    if(o.readonly) {
        readonly.value = true
    } else {
        readonly.value = false
    }
    if(o.caseId) {
        caseId.value = o.caseId
    } else {
        caseId.value = null
    }
    uni.$on('initGxUser', initSelf);
    resetForm(form)
    if(authStore.gxUser && authStore.gxUser.id) {
        await init()
    } else {
        if(authStore.wxToken) {
            await initSelf()
        }
    }
})
onUnload(async() => {
	uni.$off('initGxUser')
})

const clear = async () => {
    await nextTick()
    signatureRef.value.clear()
}
const undo = async () => {
    await nextTick()
    signatureRef.value.undo()
}
const initSelf = async () => {
    loading.value = true
    const res = await uni.$u.http.get('/gx/user/getMyInfo')
    let registerFlag = false //是否已注册
    if(res.code === 200 && res.data) {
        if(res.data.avatar) {
            authStore.gxUser = res.data
            
            await init()
            
            registerFlag = true
        }
    }
    if(!registerFlag) {
        uni.navigateTo({
            url: '/pagesGx/user/save'
        })
    }
    loading.value = false
}
const init = async () => {
    const params = {
        userId: authStore.gxUser.id,
        caseId: caseId.value,
    }
    const caseUserRes = await uni.$u.http.get('/gx/caseUser/getData' ,{params})
    if(caseUserRes.code === 200 && caseUserRes.data) {
        uToastRef.value.show({
            type: 'error',
            message: '已绑定',
            complete() {
                uni.redirectTo({url: '/pagesGx/case/user/dk/index'})
            }
        })
        return
    }

    const caseRes = await uni.$u.http.get('/gx/case/' + caseId.value)
    const data = caseRes.data
    if(caseRes.code === 200 && data) {
        Object.assign(form,data)

        if(data.dkTimeArray) {
            const array = JSON.parse(data.dkTimeArray)
            dkTimeArray.length = 0
            dkTimeArray.push(...array)
        }

        if(data.signDateArray) {
            const array = JSON.parse(data.signDateArray)
            signDateArray.length = 0
            signDateArray.push(...array)
        }

        if(data.signTimeArray) {
            const array = JSON.parse(data.signTimeArray)
            signTimeArray.length = 0
            signTimeArray.push(...array)
        }

        if(data.includesArray) {
            const array = JSON.parse(data.includesArray)
            includesArray.length = 0
            includesArray.push(...array)
        }

        if(data.excludesArray) {
            const array = JSON.parse(data.excludesArray)
            excludesArray.length = 0
            excludesArray.push(...array)
        }
    }
}
const showConfirm = async () => {
    uni.requestSubscribeMessage({
        tmplIds: ['cBV1DwpN2D7aBgvPyo9L_jWtVO1xCg055PZYKvm4Rk8'],
        async success (res:any) { 
            if(
                res['cBV1DwpN2D7aBgvPyo9L_jWtVO1xCg055PZYKvm4Rk8'] === 'accept'
            ) {
                for (const item of includesArray) {
                    if(item.value === undefined || item.value === null || item.value === '') {
                        uToastRef.value.show({
                            type: 'error',
                            message: '请完成答卷!',
                        })
                        return
                    }
                }
                for (const item of excludesArray) {
                    if(item.value === undefined || item.value === null || item.value === '') {
                        uToastRef.value.show({
                            type: 'error',
                            message: '请完成答卷!',
                        })
                        return
                    }
                }
                await nextTick()
                signatureRef.value.canvasToTempFilePath({
                    async success(temp: any) {
                        if(!temp.isEmpty) {
                            const fileRes = await uni.$u.http.upload('/common/upload',{filePath: temp.tempFilePath,name: 'file'})
                            if(fileRes.code === 200) {
                                signature.value = fileRes.url
                            }
                            submitConfirmRef.value.open()
                        } else {
                            uToastRef.value.show({
                                type: 'error',
                                message: '请签字确认',
                            })
                        }
                    }
                })
            }
        }
    })
}
const submit = async() => {
    const userId = authStore.gxUser.id
    if(userId && caseId.value) {
        const subForm = {
            caseId: caseId.value,
            userId: authStore.gxUser.id,
            signature: signature.value,
            includesArray: JSON.stringify(includesArray),
            excludesArray: JSON.stringify(excludesArray),
        }

        try {
            btnLoading.value = true
            let res = await uni.$u.http.post('/gx/caseUser',subForm)
            btnLoading.value = false
            if(res.code === 200) {
                uToastRef.value.show({
                    type: 'success',
                    message: '提交成功',
                    complete() {
                        uni.reLaunch({url: '/pagesGx/case/user/dk/index'})
                    }
                })
            } else {
                uToastRef.value.show({
                    type: 'error',
                    message: res.msg,
                    complete() {
                        uni.reLaunch({url: '/pagesGx/case/user/dk/index'})
                    }
                })
            }
        } catch (error) {
            btnLoading.value = false
        }
    }
}
</script>
<style lang="scss" scoped>

.bottom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
	background: rgba(41, 121, 255, 1);
    color: #fff;
    text-align: center;
    height: 50px;
}

</style>