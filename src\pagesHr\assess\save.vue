<template>
    <view class="form-wrapper">
        <uni-forms :modelValue="form" :rules="rules" ref="formRef">
            <uni-forms-item label="姓名" name="nickName" label-width="80px">
                <text style="line-height: 36px;">{{ form.nickName }}</text>
            </uni-forms-item>
            <uni-forms-item label="考核日期" name="assessDate" label-width="80px">
                <text style="line-height: 36px;">{{ form.assessDate }}</text>
            </uni-forms-item>
            <!-- <uni-forms-item v-for="item in propArray" :key="item.prop" :label="item.label" required :name="item.prop" label-width="80px" >
                <uni-easyinput trim="both" v-model="form[item.prop]" type="number" @input="computeScore" />
            </uni-forms-item> -->

            <uni-section title="综合能力评估" type="line" />

            <uni-table border stripe emptyText="暂无更多数据">
                <uni-tr>
                    <uni-th width="100" align="center">考核内容</uni-th>
                    <uni-th width="80" align="center">评分</uni-th>
                    <uni-th width="150" align="center">说明</uni-th>
                </uni-tr>
                <template v-for="(d, i) in dataArray">
                    <uni-tr v-for="(item, j) in d.array" :key="i + '_' + j">
                        <uni-td align="center">{{ item.label }}</uni-td>
                        <uni-td align="center">
                            <uni-easyinput trim="both" v-model="form[item.prop]" type="number" @input="computeScore"
                                :placeholder="item.out + '分'" />
                        </uni-td>
                        <uni-td align="center">{{ item.explain }}</uni-td>
                    </uni-tr>
                </template>
            </uni-table>

            <uni-forms-item label="评分" name="score" label-width="80px">
                <view style="display: flex;align-items: center;height: 100%;">
                    {{ form.score }}
                </view>
            </uni-forms-item>
            <uni-forms-item label="评价" required name="appraise" label-width="80px">
                <text :style="appraiseStyle()" style="line-height: 36px;">{{ form.appraise }}</text>
            </uni-forms-item>
            <uni-forms-item label="评语" required name="remark" label-width="80px">
                <uni-easyinput trim="both" v-model="form.remark" type="textarea" />
            </uni-forms-item>
            <uni-forms-item label="执行方式" required name="execute" label-width="80px">
                <uni-data-select v-model="form.execute" :localdata="executeOptions" placement="top" />
            </uni-forms-item>
        </uni-forms>

        <button v-if="!readonly" @click="dialogToggle" type="primary" :loading="btnLoading" :disabled="btnLoading">提
            交</button>
    </view>

    <view>
        <uni-popup ref="submitConfirmRef" type="dialog">
            <uni-popup-dialog title="注 意" @confirm="submit()">
                <template #default>
                    <text style="color: #e43d33;">评价即将发给试用期员工签字确认,是否继续?</text>
                </template>
            </uni-popup-dialog>
        </uni-popup>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { UserAssessItem } from '@/types/hr/userAssessItem';
import { onLoad } from '@dcloudio/uni-app';
import { nextTick, reactive, ref } from 'vue';
import Big from 'big.js';
import { navTo } from '@/utils/tools';

const form = reactive<UserAssessItem>({
    id: undefined,
    finish: undefined,
    efficiency: undefined,
    quality: undefined,
    enthusiasm: undefined,
    discipline: undefined,
    responsibility: undefined,
    team: undefined,
    skill: undefined,
    execution: undefined,
    study: undefined,
    dispose: undefined,
    communication: undefined,
    score: undefined,
    appraise: '',
    execute: '',
    remark: '',
});
const rules = reactive({
    execute: {
        rules: [
            { required: true, errorMessage: '请输入执行方式', },
        ],
    },
})
const btnLoading = ref(false)
const loading = ref(false)
const formRef = ref()
const submitConfirmRef = ref()
const entryUserSaveRef = ref()
const uToastRef = ref()
const executeOptions = [
    { text: '提前转正', value: '0' },
    { text: '试用期满转正', value: '1' },
    { text: '延长试用期转正', value: '2' },
    { text: '解除劳动关系', value: '3' },
]
const readonly = ref(false)
const dataArray = [
    {
        project: '工作业绩(30%)',
        array: [
            { label: '工作目标完成程度', explain: '是否出色完成每日的工作,达到目标(精确、彻底),得到认可', out: 10, prop: 'finish', score: null },
            { label: '工作效率', explain: '是否能及时按计划完成各项作业任务,时效性高', out: 10, prop: 'efficiency', score: null },
            { label: '工作质量', explain: '工作无差错,对有问题内容项及时提出并修改', out: 10, prop: 'quality', score: null },
        ],
    },
    {
        project: '工作态度(40%)',
        array: [
            { label: '积极性', explain: '是否热爱公司给予安排的工作岗位,有高标准做好职位范围内的工作', out: 10, prop: 'enthusiasm', score: null },
            { label: '纪律性', explain: '是否遵守公司各项规章制度及上级指示,忠于自己的岗位,表里一致的进行工作', out: 10, prop: 'discipline', score: null },
            { label: '责任感', explain: '自觉把握在工作中的角色,遇到困难不屈不挠完成工作的意志,对自己的工作行为表示负责的态度', out: 10, prop: 'responsibility', score: null },
            { label: '团队意识', explain: '团队内部沟通的能力及意识', out: 10, prop: 'team', score: null },
        ],
    },
    {
        project: '工作能力(30%)',
        array: [
            { label: '基本知识技能', explain: '掌握试用期内所在岗位应具备的知识、技能,达到该岗位需求认知的基准', out: 11, prop: 'skill', score: null },
            { label: '执行能力', explain: '是否理解工作要求,动手、实操能力强,处理灵活,独立承担本职工作范围内的工作任务', out: 6, prop: 'execution', score: null },
            { label: '学习能力', explain: '勤奋好学,努力学习各项与工作相关的工作技能,更好的完成工作任务', out: 4, prop: 'study', score: null },
            { label: '处理问题能力', explain: '对失误和突如其来的工作,措施是否迅速恰当', out: 4, prop: 'dispose', score: null },
            { label: '表达能力', explain: '能否快速融入团队,各项工作对接交流无间,尊重每个岗位', out: 5, prop: 'communication', score: null },
        ],
    },
]
const propArray = [
    { label: '完成程度', prop: 'finish', max: 10 },
    { label: '工作效率', prop: 'efficiency', max: 10 },
    { label: '工作质量', prop: 'quality', max: 10 },
    { label: '积极性', prop: 'enthusiasm', max: 10 },
    { label: '纪律性', prop: 'discipline', max: 10 },
    { label: '责任感', prop: 'responsibility', max: 10 },
    { label: '团队意识', prop: 'team', max: 10 },
    { label: '知识技能', prop: 'skill', max: 10 },
    { label: '执行能力', prop: 'execution', max: 10 },
    { label: '学习能力', prop: 'study', max: 10 },
    { label: '处理问题', prop: 'dispose', max: 10 },
    { label: '表达能力', prop: 'communication', max: 10 },
]

onLoad(async (o: any) => {
    if (o.id) {
        await init(o.id)
    }
    if (o.readonly) {
        readonly.value = true
    } else {
        readonly.value = false
    }

    // for(const item of propArray) {
    //     rules[item.prop] = {
    //         rules: [
    //             {required: true, errorMessage: '请输入' + item.label,},
    //             {
    //                 validateFunction(rule:any,value: number,data:any,callback:Function){
    //                     if (value < 0) {
    //                         callback(item.label + '不能小于0')
    //                     }
    //                     if(value > item.max) {
    //                         callback(item.label + '不能大于' + item.max)
    //                     }
    //                     return true
    //                 }
    //             }
    //         ],
    //     }
    // }
})

const dialogToggle = () => {
    submitConfirmRef.value.open()
}
const appraiseStyle = () => {
    const score = form.score
    let color = '';
    if (score !== undefined) {
        if (score >= 95 && score <= 100) {
            color = '#409EFF'
        } else if (score >= 85 && score < 95) {
            color = '#67C23A'
        } else if (score >= 70 && score < 85) {
            color = '#E6A23C'
        } else if (score >= 61 && score < 70) {
            color = '#F56C6C'
        } else if (score >= 0 && score < 61) {
            color = '#909399'
        }
    }
    return {
        color: color,
    }
}
const computeScore = async () => {
    await nextTick()
    let score = Big(0)
    for (const item of propArray) {
        if (form[item.prop]) {
            score = score.plus(form[item.prop])
        }
    }
    score = score.toNumber()

    form.score = score
    if (score >= 95 && score <= 100) {
        form.appraise = '卓越'
        form.execute = '0'
    } else if (score >= 85 && score < 95) {
        form.appraise = '优秀'
        form.execute = '1'
    } else if (score >= 70 && score < 85) {
        form.appraise = '能胜任本职工作'
        form.execute = '1'
    } else if (score >= 61 && score < 70) {
        form.appraise = '部分胜任本职工作'
        form.execute = '2'
    } else if (score >= 0 && score < 61) {
        form.appraise = '不能胜任本职工作'
        form.execute = '3'
    }
}
const init = async (id: Number) => {
    loading.value = true
    const res = await uni.$u.http.get('/user/assess/item/' + id)
    loading.value = false
    if (res.code === 200) {
        const data = res.data
        Object.assign(form, data)

        await nextTick()
        if (entryUserSaveRef.value && entryUserSaveRef.value.init) {
            entryUserSaveRef.value.readonly = true
            entryUserSaveRef.value.init(form.userId)
        }
    }
}
const submit = async () => {
    const validateRes = await formRef.value.validate()
    const subForm = Object.assign({}, form)
    for (const item of propArray) {
        if (subForm[item.prop] !== undefined && subForm[item.prop] >= 0) {
            if (subForm[item.prop] > item.max) {
                uToastRef.value.show({
                    type: 'error',
                    message: item.label + '超过评分上限',
                })
                return;
            }
        } else {
            uToastRef.value.show({
                type: 'error',
                message: '请输入评分',
            })
            return;
        }
    }

    let res;
    try {
        btnLoading.value = true
        if (subForm.id != null) {
            res = await uni.$u.http.put('/user/assess/item/appraise', subForm)
        }
        btnLoading.value = false
        if (res.code === 200) {
            uToastRef.value.show({
                type: 'success',
                message: '提交成功',
                complete() {
                    uni.$emit('refreshUserAssessItem')
                    uni.navigateBack({
                        delta: 1
                    })
                }
            })
        } else {
            uToastRef.value.show({
                type: 'error',
                message: res.msg,
            })
        }
    } catch (error) {
        btnLoading.value = false
    }
}

</script>
<style lang="scss" scoped></style>