<template>
    <view class="form-wrapper" >
        <uni-forms :modelValue="form" :rules="rules" ref="formRef" label-width="120" >
            <uni-forms-item label="客户编号" name="code" >
                <span style="line-height: 36px;" >{{ form.code }}</span>
            </uni-forms-item>
            <uni-forms-item label="客户名称" required name="name" >
                <uni-easyinput trim="both" v-model="form.name" />
            </uni-forms-item>
            <uni-forms-item label="客户简称" name="shortName" >
                <uni-easyinput trim="both" v-model="form.shortName" />
            </uni-forms-item>
            <uni-forms-item label="客户级别" name="customerLevel" >
                <uni-data-select v-model="form.customerLevel" :localdata="customerLevelOptions" />
            </uni-forms-item>
            <uni-forms-item label="客户类型" name="customerType" >
                <uni-data-select v-model="form.customerType" :localdata="customerTypeOptions" />
            </uni-forms-item>
            <uni-forms-item label="客户来源" name="resource" >
                <uni-data-select v-model="form.resource" :localdata="resourceOptions" />
            </uni-forms-item>
            <uni-forms-item label="客户状态" name="customerStatus" >
                <uni-data-select v-model="form.customerStatus" :localdata="customerStatusOptions" />
            </uni-forms-item>
            <uni-forms-item label="代理公司" name="agent" >
                <uni-easyinput trim="both" v-model="form.agent" />
            </uni-forms-item>
            <uni-forms-item label="集团" name="customerGroup" >
                <uni-easyinput trim="both" v-model="form.customerGroup" />
            </uni-forms-item>
            <uni-forms-item label="所属区域" name="customerArea" >
                <uni-data-select v-model="form.customerArea" :localdata="customerAreaOptions" />
            </uni-forms-item>
            <uni-forms-item label="自有工厂" name="hasFactory" >
                <uni-data-select v-model="form.hasFactory" :localdata="whetherOptions" />
            </uni-forms-item>
            <uni-forms-item label="年销售额" name="yearSales" >
                <uni-easyinput trim="both" v-model="form.yearSales" type="number" />
            </uni-forms-item>
            <uni-forms-item label="是否上市公司" name="isListedCompany" >
                <uni-data-select v-model="form.isListedCompany" :localdata="whetherOptions" />
            </uni-forms-item>
            <uni-forms-item label="是否出口" name="isExit" >
                <uni-data-select v-model="form.isExit" :localdata="whetherOptions" />
            </uni-forms-item>
            <uni-forms-item label="出口国家" name="exitCountry" >
                <uni-easyinput trim="both" v-model="form.exitCountry" />
            </uni-forms-item>
            <uni-forms-item label="客户描述" name="customerRemark" >
                <uni-easyinput trim="both" v-model="form.customerRemark" type="textarea" />
            </uni-forms-item>
        </uni-forms>
        <button v-if="!readonly" @click="submit" type="primary" :loading="btnLoading" :disabled="btnLoading" >提 交</button>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { Customer } from '@/types/customer';
import { getDictData, resetForm } from '@/utils/tools';
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';

const form = reactive<Customer>({
    id: undefined,
    name: '',
    shortName: '',
    customerLevel: '',
    customerType: '',
    resource: '',
    customerStatus: '',
    agent: '',
    customerGroup: '',
    customerArea: '',
    hasFactory: undefined,
    yearSales: undefined,
    isListedCompany: undefined,
    isExit: undefined,
    exitCountry: '',
    customerRemark: '',
    remark: '',
});
const rules = reactive({
	name: {
		rules: [
			{required: true, errorMessage: '请输入客户名称',},
		]
	},
})
const btnLoading = ref(false)
const loading = ref(false)
const formRef = ref()
const uToastRef = ref()
const customerTypeOptions = [
    {value: '潜在客户',text: '潜在客户'},
    {value: '新客户',text: '新客户'},
    {value: '老客户',text: '老客户'},
]
const resourceOptions = [
    {value: '公司资源',text: '公司资源'},
    {value: '客户推荐',text: '客户推荐'},
    {value: '展会拓展',text: '展会拓展'},
    {value: '自主开发',text: '自主开发'},
    {value: '其他内部客户',text: '其他内部客户'},
]
const whetherOptions = [
    {value: 0,text: '否'},
    {value: 1,text: '是'},
]
const customerLevelOptions = getDictData('CUSTOMER_LEVEL')
const customerStatusOptions = getDictData('CUSTOMER_STATUS')
const customerAreaOptions = getDictData('CUSTOMER_AREA')
const readonly = ref(false)

onLoad(async (o:any)=> {
    if(o.id) {
        await init(o.id)
    }
    if(o.readonly) {
        readonly.value = true
    } else {
        readonly.value = false
    }
})

const init = async (id: Number) => {
    loading.value = true
    const res = await uni.$u.http.get('/customer/' + id)
    loading.value = false
    if(res.code === 200) {
        const data = res.data
        Object.assign(form, data)
    }
}
const submit = async() => {
    const validateRes = await formRef.value.validate()
    const subForm = Object.assign({},form)
    let res;
    try {
        btnLoading.value = true
        if (subForm.id != null) {
            res = await uni.$u.http.put('/customer',subForm)
        } else {
            res = await uni.$u.http.post('/customer',subForm)
        }
        btnLoading.value = false
        if(res.code === 200) {
            uToastRef.value.show({
				type: 'success',
				message: '提交成功',
				complete() {
                    uni.$emit('refreshCustomer')
					uni.navigateBack({
						delta: 1
					})
				}
			})
        } else {
            uToastRef.value.show({
				type: 'error',
				message: res.msg,
			})
        }
	} catch (error) {
        btnLoading.value = false
    }

}

</script>
<style lang="scss" scoped>
</style>