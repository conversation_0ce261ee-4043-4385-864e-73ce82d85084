// import Request from 'luch-request'

// const http = new Request({
//   baseURL: import.meta.env.VITE_BASE_URL,
//   timeout: 30000,
//   header: {
//     'Content-Type': 'application/json;charset=UTF-8;',
//   },
// })

// http.interceptors.request.use(
//   (config) => {
//     const auth = uni.getStorageSync('auth')
//     if(auth) {
//       const authJson = JSON.parse(auth)
//       if (authJson.token) {
//         config.header = config.header || {}
//         config.header.Authorization = 'EnowRequestBearer ' + authJson.token;
//       }
//     }
//     return config
//   },
//   (error) => {
//     return Promise.resolve(error)
//   },
// )

// http.interceptors.response.use(
//   (response) => {
//     if (response.statusCode == 200) {
//       return response.data
//     }
//     return response
//   },
//   (error) => {
//     //未登录时清空缓存跳转
//     if (error.statusCode == 401) {
//       uni.clearStorageSync()
//       uni.switchTab({
//         url: '/pages/index/index.vue',
//       })
//     }

//     const { errMsg } = error

//     uni.showToast({
//       title: errMsg,
//       duration: 2000,
//     })
//     return Promise.resolve(error)
//   },
// )
// export default http
