<template>
  <view style="width: 100vw; height: 100vh">
    <l-signature ref="signatureRef" landscape />
  </view>
  <view
    style="
      transform: rotate(90deg);
      position: fixed;
      top: 200rpx;
      left: -100rpx;
    "
  >
    <button type="warn" @click="clear" size="mini">清空</button>
    <button @click="undo" size="mini">上一步</button>
    <button @click="confirm" size="mini" type="primary">提 交</button>
  </view>
</template>
<script lang="ts" setup>
import { ref, nextTick } from "vue";

const signatureRef = ref();

const clear = async () => {
  await nextTick();
  signatureRef.value.clear();
};
const undo = async () => {
  await nextTick();
  signatureRef.value.undo();
};
const confirm = async () => {
  await nextTick();
  signatureRef.value.canvasToTempFilePath({
    async success(temp: any) {
      if (!temp.isEmpty) {
        const fileRes = await uni.$u.http.upload("/common/upload", {
          filePath: temp.tempFilePath,
          name: "file",
        });
        if (fileRes.code === 200) {
          uni.$emit("sign", fileRes.url);
          uni.navigateBack({
            delta: 1,
          });
        }
      } else {
        uToastRef.value.show({
          type: "error",
          message: "请签名确认!",
        });
        return;
      }
    },
  });
};
</script>
