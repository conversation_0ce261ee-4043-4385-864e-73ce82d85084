<template>
    <view class="form-wrapper">

        <view class="avatar-wrapper">
            <image style="width: 100px;height: 40px;"
                src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240521/1716270633460.png" />
            <button plain class="avatar-btn" @click="avatarClick" :loading="btnLoading">
                <up-avatar v-if="form.avatar" :src="form.avatar" :size="60" />
                <up-avatar v-else icon="plus" :size="60" />
            </button>
            <view class="tips" v-if="!form.avatar">请上传头像</view>
            <image style="width: 375px;height: 60px;"
                src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240521/1716270639502.png" />
        </view>

        <uni-forms :modelValue="form" :rules="rules" ref="formRef">
            <uni-forms-item label="姓名" required name="userName">
                <uni-easyinput trim="both" v-model="form.userName" />
            </uni-forms-item>
            <uni-forms-item label="应聘日期" name="applicationDate">
                <uni-datetime-picker type="date" v-model="form.applicationDate" />
            </uni-forms-item>
            <uni-forms-item label="应聘岗位" required name="applicationPosition">
                <uni-easyinput trim="both" v-model="form.applicationPosition" />
            </uni-forms-item>
            <uni-forms-item label="如被录用预计报到日期" name="onDutyDate">
                <uni-easyinput trim="both" v-model="form.onDutyDate" />
            </uni-forms-item>

            <uni-section title="个人基本信息" type="line" />

            <uni-forms-item label="性别" name="sex">
                <uni-data-checkbox v-model="form.sex" :localdata="userSexOptions" />
            </uni-forms-item>
            <uni-forms-item label="邮箱" name="email">
                <uni-easyinput trim="both" v-model="form.email" />
            </uni-forms-item>
            <uni-forms-item label="手机号" required name="phonenumber">
                <uni-easyinput trim="both" v-model="form.phonenumber" type="number" />
            </uni-forms-item>
            <uni-forms-item label="固定电话" name="gddh">
                <uni-easyinput trim="both" v-model="form.gddh" />
            </uni-forms-item>
            <uni-forms-item label="身份证号" required name="cardNo">
                <uni-easyinput trim="both" v-model="form.cardNo" type="idcard" />
            </uni-forms-item>
            <uni-forms-item label="户籍类别" required name="hjlb">
                <uni-data-select v-model="form.hjlb" :localdata="hukouXzOptions" />
            </uni-forms-item>
            <uni-forms-item label="户籍" name="hukouJg">
                <uni-easyinput trim="both" v-model="form.hukouJg" />
            </uni-forms-item>
            <uni-forms-item label="户籍地址" name="hukouHjdz">
                <uni-easyinput trim="both" v-model="form.hukouHjdz" type="textarea" />
            </uni-forms-item>
            <uni-forms-item label="出生地" name="birthplace">
                <uni-easyinput trim="both" v-model="form.birthplace" />
            </uni-forms-item>
            <uni-forms-item label="现居住地址" required name="hukouXjjdz">
                <uni-easyinput trim="both" v-model="form.hukouXjjdz" type="textarea" />
            </uni-forms-item>
            <uni-forms-item label="政治面貌" name="politicalFace">
                <uni-data-select v-model="form.politicalFace" :localdata="politicalFaceOptions" />
            </uni-forms-item>
            <uni-forms-item label="体重" name="weight">
                <uni-easyinput trim="both" v-model="form.weight" type="number">
                    <template #right>
                        <view>KG</view>
                    </template>
                </uni-easyinput>
            </uni-forms-item>
            <uni-forms-item label="身高" name="height">
                <uni-easyinput trim="both" v-model="form.height" type="number">
                    <template #right>
                        <view>CM</view>
                    </template>
                </uni-easyinput>
            </uni-forms-item>
            <uni-forms-item label="民族" name="ethnic">
                <uni-easyinput trim="both" v-model="form.ethnic" />
            </uni-forms-item>
            <uni-forms-item label="最高学历" required name="degree">
                <uni-data-select v-model="form.degree" :localdata="degreeOptions" />
            </uni-forms-item>
            <uni-forms-item label="教育类别" name="firstDegree">
                <uni-data-select v-model="form.firstDegree" :localdata="firstDegreeOptions" />
            </uni-forms-item>
            <uni-forms-item label="学位" name="xuewei">
                <uni-easyinput trim="both" v-model="form.xuewei" />
            </uni-forms-item>
            <uni-forms-item label="外语语种" name="yzzs">
                <uni-easyinput trim="both" v-model="form.yzzs" />
            </uni-forms-item>
            <uni-forms-item label="外语口语能力" name="wykynl">
                <uni-easyinput trim="both" v-model="form.wykynl" />
            </uni-forms-item>
            <template v-if="confirmFlag">
                <uni-forms-item label="银行" name="bankName">
                    <uni-easyinput trim="both" v-model="form.bankName" />
                </uni-forms-item>
                <uni-forms-item label="银行卡号" name="bankNo">
                    <uni-easyinput trim="both" v-model="form.bankNo" />
                </uni-forms-item>
                <uni-forms-item label="开户行" name="bankAccount">
                    <uni-easyinput trim="both" v-model="form.bankAccount" />
                </uni-forms-item>
            </template>
            <uni-forms-item label="婚姻状况" required name="hyzk">
                <uni-data-select v-model="form.hyzk" :localdata="hyzkOptions" />
            </uni-forms-item>
            <uni-forms-item label="子女情况(男孩)" name="znzkMan">
                <uni-easyinput trim="both" v-model="form.znzkMan" type="number">
                    <template #right>
                        <view>个</view>
                    </template>
                </uni-easyinput>
            </uni-forms-item>
            <uni-forms-item label="子女情况(女孩)" name="znzkWoman">
                <uni-easyinput trim="both" v-model="form.znzkWoman" type="number">
                    <template #right>
                        <view>个</view>
                    </template>
                </uni-easyinput>
            </uni-forms-item>
            <uni-forms-item label="学历证书" required name="imgs">
                <ImageUpload v-model="form.imgs" :mutiple="true" />
            </uni-forms-item>

            <template v-if="confirmFlag">
                <uni-section title="身份证" type="line" />

                <uni-forms-item label="正面" required name="cardBeforeImg">
                    <ImageUpload v-model="form.cardBeforeImg" />
                </uni-forms-item>
                <uni-forms-item label="反面" required name="cardAfterImg">
                    <ImageUpload v-model="form.cardAfterImg" />
                </uni-forms-item>
            </template>

            <uni-section title="家庭成员情况">
                <template v-slot:decoration>
                    <text class="required">*</text>
                </template>
            </uni-section>

            <uni-table border stripe emptyText="暂无更多数据">
                <uni-tr>
                    <uni-th width="50" align="center">
                        <uni-icons type="plus" @click="addFamilyItem" />
                    </uni-th>
                    <uni-th align="center">姓名</uni-th>
                    <uni-th align="center">手机号</uni-th>
                    <uni-th align="center">关系</uni-th>
                    <uni-th align="center">年龄</uni-th>
                    <uni-th align="center">工作单位</uni-th>
                    <uni-th align="center">所在城市</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, i) in familyList" :key="item.id">
                    <uni-td align="center">
                        <uni-icons type="minus" @click="delFamilyItem(i)" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.name" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.mobileNo" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.gx" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.age" type="number" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.gzdw" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.city" />
                    </uni-td>
                </uni-tr>
            </uni-table>

            <uni-section title="紧急联系人(必要时需通知之亲友)">
                <template v-slot:decoration>
                    <text class="required">*</text>
                </template>
            </uni-section>

            <uni-table border stripe emptyText="暂无更多数据">
                <uni-tr>
                    <uni-th width="50" align="center">
                        <uni-icons type="plus" @click="addContactItem" />
                    </uni-th>
                    <uni-th align="center">姓名</uni-th>
                    <uni-th align="center">关系</uni-th>
                    <uni-th align="center">联系电话</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, i) in contactList" :key="item.id">
                    <uni-td align="center">
                        <uni-icons type="minus" @click="delContactItem(i)" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.name" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.gx" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.mobileNo" />
                    </uni-td>
                </uni-tr>
            </uni-table>

            <uni-section title="学习经历(请从最高学历开始填写)">
                <template v-slot:decoration>
                    <text class="required">*</text>
                </template>
            </uni-section>

            <uni-table border stripe emptyText="暂无更多数据">
                <uni-tr>
                    <uni-th width="50" align="center">
                        <uni-icons type="plus" @click="addDegreeItem" />
                    </uni-th>
                    <uni-th width="160" align="center">开始时间</uni-th>
                    <uni-th width="160" align="center">结束时间</uni-th>
                    <uni-th align="center">学校名称</uni-th>
                    <uni-th align="center">专业</uni-th>
                    <uni-th align="center">学历</uni-th>
                    <uni-th align="center">学位</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, i) in degreeList" :key="item.id">
                    <uni-td align="center">
                        <uni-icons type="minus" @click="delDegreeItem(i)" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.startDate" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.endDate" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.school" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.professional" />
                    </uni-td>
                    <uni-td align="center">
                        <!-- <uni-data-select v-model="item.degree" :placement="(i === (degreeList.length-1))?'top':'bottom'" :localdata="degreeOptions" /> -->
                        <uni-easyinput trim="both" v-model="item.degree" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.remark" />
                    </uni-td>
                </uni-tr>
            </uni-table>

            <uni-section title="主要工作经历(请从最近一家开始填写)">
                <template v-slot:decoration>
                    <text class="required">*</text>
                </template>
            </uni-section>

            <uni-table border stripe emptyText="暂无更多数据">
                <uni-tr>
                    <uni-th width="50" align="center">
                        <uni-icons type="plus" @click="addJobItem" />
                    </uni-th>
                    <uni-th width="160" align="center">开始时间</uni-th>
                    <uni-th width="160" align="center">结束时间</uni-th>
                    <uni-th align="center">工作单位名称</uni-th>
                    <uni-th align="center">职位</uni-th>
                    <uni-th align="center">离职原因</uni-th>
                    <uni-th align="center">证明人</uni-th>
                    <uni-th align="center">证明人联系电话</uni-th>
                    <uni-th align="center">人事部门联系人</uni-th>
                    <uni-th align="center">人事部固定电话</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, i) in jobList" :key="item.id">
                    <uni-td align="center">
                        <uni-icons type="minus" @click="delJobItem(i)" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.startDate" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.endDate" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.company" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.post" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.lzyy" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.certifier" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.certifierPhone" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.rsbmlxr" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.rsbmdh" />
                    </uni-td>
                </uni-tr>
            </uni-table>

            <uni-section title="主要培训经历">
                <template v-slot:decoration>
                    <text class="required">*</text>
                </template>
            </uni-section>

            <uni-table border stripe emptyText="暂无更多数据">
                <uni-tr>
                    <uni-th width="50" align="center">
                        <uni-icons type="plus" @click="addTrainingItem" />
                    </uni-th>
                    <uni-th width="160" align="center">开始时间</uni-th>
                    <uni-th width="160" align="center">结束时间</uni-th>
                    <uni-th align="center">培训机构</uni-th>
                    <uni-th align="center">培训课程</uni-th>
                    <uni-th width="100" align="center">培训证书</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, i) in trainingList" :key="item.id">
                    <uni-td align="center">
                        <uni-icons type="minus" @click="delTrainingItem(i)" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.startDate" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.endDate" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.institution" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.course" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.result" />
                    </uni-td>
                </uni-tr>
            </uni-table>

            <uni-section title="薪资福利期望" type="line" />

            <view style="display: flex;align-items: center;color: #e43d33;">
                <text>薪资低于</text>
                <uni-easyinput trim="both" v-model="form.zdxz" type="number" :inputBorder="false"
                    style="width: 100px;border-bottom: 1px #b9b9b9 solid" />
                <text>元/月(税前)本人不予考虑</text>
            </view>
            <view style="display: flex;align-items: center;color: #e43d33;">
                <text>最后一家就职单位薪资合计</text>
                <uni-easyinput trim="both" v-model="form.zhyjxzHj" :inputBorder="false"
                    style="width: 100px;border-bottom: 1px #b9b9b9 solid" type="number" />
                <text>元/月</text>
            </view>

            <uni-forms-item label="组成情况" name="zhyjxz">
                <uni-easyinput trim="both" v-model="form.zhyjxz" type="textarea" />
            </uni-forms-item>
            <uni-forms-item label="其他" name="zdxzOther">
                <uni-easyinput trim="both" v-model="form.zdxzOther" type="textarea" />
            </uni-forms-item>

            <uni-section title="其它相关事项" type="line" />

            <uni-table border stripe emptyText="暂无更多数据">
                <uni-tr>
                    <uni-th width="300" align="center">问题</uni-th>
                    <uni-th width="80" align="center">答案</uni-th>
                    <uni-th width="180" align="center">备注</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, i) in questionArray" :key="item.id">
                    <uni-td>
                        {{ item.question }}
                    </uni-td>
                    <uni-td align="center">
                        <uni-data-select v-model="item.whether"
                            :placement="(i === (questionArray.length - 1)) ? 'top' : 'bottom'"
                            :localdata="whetherOptions" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.remark" />
                    </uni-td>
                </uni-tr>
            </uni-table>

            <view style="margin: 10px 0">您是在何处得知本岗位的招聘信息的:</view>

            <uni-data-checkbox v-model="form.zpxx" :localdata="resourceOptions" />

            <view style="height: 15px" />

            <uni-forms-item label="其他途径" name="zpxxOther">
                <uni-easyinput trim="both" v-model="form.zpxxOther" type="textarea" />
            </uni-forms-item>

            <uni-forms-item label="附件" name="files">
                <FileUpload v-model="form.files" />
            </uni-forms-item>
        </uni-forms>

        <view v-if="confirmFlag" style="display: flex;align-items: center;justify-content: space-between;">
            <button @click="submit('1')" type="primary" size="mini" :loading="btnLoading"
                :disabled="btnLoading">确认入职</button>
            <button @click="submit('2')" type="error" size="mini" :loading="btnLoading"
                :disabled="btnLoading">拒绝入职</button>
        </view>

        <template v-else>
            <view class="tips-warpper">
                <text class="tips">我承诺以上信息均为真实记录，如有虚假成分，将导致录用后被解除劳动关系的后果，并承担相关法律责任。</text>
            </view>

            <l-signature ref="signatureRef" />

            <view class="btn-wrapper">
                <button type="warn" @click="clear" size="mini">清空</button>
                <button @click="undo" size="mini">上一步</button>
                <button v-if="!readonly" @click="dialogToggle()" type="primary" :loading="btnLoading" size="mini"
                    :disabled="btnLoading">提 交</button>
            </view>
        </template>

    </view>

    <view>
        <uni-popup ref="submitConfirmRef" type="dialog">
            <uni-popup-dialog title="声 明" @confirm="submit()">
                <template #default>
                    <text style="color: #e43d33;">我承诺以上信息均为真实记录，如有虚假成分，将导致录用后被解除劳动关系的后果，并承担相关法律责任。</text>
                </template>
            </uni-popup-dialog>
        </uni-popup>
    </view>

    <view>
        <uni-popup ref="navConfirmRef" type="dialog">
            <uni-popup-dialog title="注 意" @confirm="dialogConfirm" :showClose="false">
                <template #default>
                    <text style="color: #e43d33;">面试登记已完成,下一步即将进入职业测试,您准备好了吗?</text>
                </template>
            </uni-popup-dialog>
        </uni-popup>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { EntryUser } from '@/types/hr/entryUser';
import { getDictData, navTo, resetForm } from '@/utils/tools';
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref, inject, nextTick } from 'vue';
import ImageUpload from '@/components/imageUpload.vue'
import FileUpload from '@/components/fileUpload.vue'
import { useAuthStore } from '@/state/modules/user';

const signatureRef = ref()
const dayjs: any = inject('dayjs')
const authStore = useAuthStore();
const form = reactive<EntryUser>({
    userId: undefined,
    avatar: '',
    userName: '',
    applicationDate: '',
    applicationPosition: '',
    onDutyDate: '',
    sex: '',
    birthday: '',
    age: undefined,
    email: '',
    phonenumber: '',
    gddh: '',
    cardNo: '',
    hukouHjdz: '',
    birthplace: '',
    hukouXjjdz: '',
    ethnic: '',
    hjlb: '',
    politicalFace: '',
    weight: undefined,
    height: undefined,
    degree: '',
    firstDegree: '',
    xuewei: '',
    yzzs: '',
    wykynl: '',
    hyzk: '',
    znzkMan: '',
    znzkWoman: '',
    zdxz: undefined,
    zdxzOther: '',
    zhyjxz: '',
    zhyjxzHj: undefined,
    ahtc: '',
    sfshjzz: '',
    sffz: '',
    sfhy: '',
    sfzb: '',
    sfzbOther: '',
    sfqs: '',
    sfqsOther: '',
    sfgl: '',
    sfglOther: '',
    sfglqs: '',
    sfglqsOther: '',
    zpxx: '',
    zpxxOther: '',
    imgs: '',
    files: '',
    questionArray: '',
    hukouJg: '',
    bankName: '',
    bankNo: '',
    bankAccount: '',
    cardBeforeImg: '',
    cardAfterImg: '',
    signature: '',
});
const rules = reactive({
    userName: {
        rules: [
            { required: true, errorMessage: '请输入姓名', },
        ]
    },
    applicationPosition: {
        rules: [
            { required: true, errorMessage: '请输入应聘岗位', },
        ]
    },
    cardNo: {
        rules: [
            {
                required: true,
                errorMessage: '请输入身份证',
            }
        ]
    },
    phonenumber: {
        rules: [
            {
                required: true,
                errorMessage: '请输入手机号',
            },
            {
                pattern: '^1[3-9]\\d{9}$',
                errorMessage: '手机号格式不正确',
            },
        ]
    },
    hyzk: {
        rules: [
            {
                required: true,
                errorMessage: '请输入婚姻状况',
            },
        ]
    },
    hukouXjjdz: {
        rules: [
            {
                required: true,
                errorMessage: '请输入现居住地址',
            },
        ]
    },
    hjlb: {
        rules: [
            {
                required: true,
                errorMessage: '请输入户籍类别',
            },
        ]
    },
    degree: {
        rules: [
            {
                required: true,
                errorMessage: '请输入最高学历',
            },
        ]
    },
    zhyjxzHj: {
        rules: [
            {
                required: true,
                errorMessage: '请输入最后一家最低薪资合计',
            },
        ]
    },
    cardBeforeImg: {},
    cardAfterImg: {},
})
const files: File[] = reactive([])
const familyList: any[] = reactive([])
const contactList: any[] = reactive([])
const degreeList: any[] = reactive([])
const jobList: any[] = reactive([])
const trainingList: any[] = reactive([])
const questionArray: any[] = reactive([])
const btnLoading = ref(false)
const loading = ref(false)
const formRef = ref()
const uToastRef = ref()
const submitConfirmRef = ref()
const navConfirmRef = ref()
const userSexOptions = getDictData('sys_user_sex')
const politicalFaceOptions = getDictData('sys_user_politicalFace')
const hukouXzOptions = getDictData('sys_user_hukouXz')
const whetherOptions = [
    { value: '0', text: '否' },
    { value: '1', text: '是' },
]
const resourceOptions = [
    { value: '0', text: '网络' },
    { value: '1', text: '报刊杂志' },
    { value: '2', text: '校园' },
    { value: '3', text: '亲朋推荐' },
    { value: '4', text: '招聘会' },
    { value: '5', text: '猎头' },
    { value: '6', text: '职介所/劳务公司' },
]
const degreeOptions = getDictData('sys_user_degree')
const firstDegreeOptions = getDictData('sys_user_first_degree')
const hyzkOptions = getDictData('sys_user_hyzk')
const readonly = ref(false)
const confirmFlag = ref(false)

onLoad(async (o: any) => {
    loading.value = true
    if (!uni.getStorageSync('dict_data')) {
        let dictRes = await uni.$u.http.get('/system/dict/all')
        uni.setStorageSync('dict_data', dictRes)
        uni.reLaunch({ url: '/pages/hr/apply' })
    }
    if (o.readonly) {
        readonly.value = true
    } else {
        readonly.value = false
    }
    if (o.confirmFlag) {
        confirmFlag.value = true
    } else {
        confirmFlag.value = false
    }
    resetForm(form)
    if (o.userId) {
        await init(o.userId)
    } else if (authStore.wxToken) {
        await initSelf()
    }
    loading.value = false
    uni.showModal({
        title: '提示',
        content: '为了方便及时联系到您，请 点击右上角三个点-设置-订阅消息/通知管理-接收所有通知',
    })
})

const clear = async () => {
    await nextTick()
    signatureRef.value.clear()
}
const undo = async () => {
    await nextTick()
    signatureRef.value.undo()
}
const dialogToggle = () => {
    uni.requestSubscribeMessage({
        tmplIds: ['PoQJ37DgToziU5QlUiHsgkzH-BjmfCz6tcMjgKJwWXc', '_Yf-oEtTZ-Nxh6q_5Bhz_kTz4itYHzE9Tdtsaad9XEs'],
        success(res: any) {
            if (
                res['PoQJ37DgToziU5QlUiHsgkzH-BjmfCz6tcMjgKJwWXc'] === 'accept'
                && res['_Yf-oEtTZ-Nxh6q_5Bhz_kTz4itYHzE9Tdtsaad9XEs'] === 'accept'
            ) {
                submit()
            }
        }
    })
}
const dialogConfirm = () => {
    navTo('/pages/hr/qa')
}
const avatarClick = async () => {
    try {
        btnLoading.value = true
        const res = await uni.chooseImage({ count: 1 })
        if (res.errMsg === 'chooseImage:ok' && res.tempFilePaths) {
            const filePath = res.tempFilePaths[0]
            const uploadRes = await uni.$u.http.upload('/common/upload', { filePath, name: 'file' })
            if (uploadRes.code === 200) {
                form.avatar = uploadRes.url
            }
        }
        btnLoading.value = false
    } catch (error) {
        btnLoading.value = false
    }
}
const resetQuestionArray = () => {
    questionArray.length = 0
    questionArray.push({ question: '是否拥有上海市居住证', answer: '', remark: '' })
    questionArray.push({ question: '是否曾有犯罪记录', answer: '', remark: '' })
    questionArray.push({ question: '女员工请说明现时是否怀孕', answer: '', remark: '' })
    questionArray.push({ question: '是否患有重病、传染病或动过重大手术', answer: '', remark: '' })
    questionArray.push({ question: '是否有亲属或朋友在宜侬生物科技工作', answer: '', remark: '' })
    questionArray.push({ question: '以前是否曾在宜侬生物科技关联代理公司工作', answer: '', remark: '' })
    questionArray.push({ question: '是否与应聘的代理公司有亲属或朋友关系', answer: '', remark: '' })
}
const initSelf = async () => {
    const res = await uni.$u.http.get('/entry/user/getMyInfo')
    if (res.code === 200 && res.data) {
        const data = res.data
        buildForm(data)
    } else {
        resetQuestionArray()
    }
}
const init = async (userId: Number) => {
    const res = await uni.$u.http.get('/entry/user/' + userId)
    if (res.code === 200 && res.data) {
        const data = res.data
        buildForm(data)
    } else {
        resetQuestionArray()
    }
}
const buildForm = (data: EntryUser) => {
    // if(data.discResult) {
    //     uToastRef.value.show({
    //         type: 'error',
    //         message: '您已经提交过答题了,请等待面试结果',
    //         complete() {
    //             uni.reLaunch({url: '/pages/index/index'})
    //         }
    //     })
    // }

    Object.assign(form, data)

    if (data.familyList) {
        familyList.length = 0
        familyList.push(...data.familyList)
    }
    if (data.contactList) {
        contactList.length = 0
        contactList.push(...data.contactList)
    }
    if (data.degreeList) {
        degreeList.length = 0
        degreeList.push(...data.degreeList)
    }
    if (data.jobList) {
        jobList.length = 0
        jobList.push(...data.jobList)
    }
    if (data.trainingList) {
        trainingList.length = 0
        trainingList.push(...data.trainingList)
    }

    resetQuestionArray()

    if (data.questionArray) {
        const questionTempArray = JSON.parse(data.questionArray)
        if (questionTempArray.length) {
            questionArray.length = 0
            questionArray.push(...questionTempArray)
        }
    }

}
const submit = async (confirmStatus?: string) => {
    if (confirmFlag.value) {
        rules.cardBeforeImg = {
            rules: [
                {
                    required: true,
                    errorMessage: '请上传身份证正面',
                },
            ]
        }
        rules.cardAfterImg = {
            rules: [
                {
                    required: true,
                    errorMessage: '请上传身份证反面',
                },
            ]
        }

    }
    try {
        const validateRes = await formRef.value.validate()
    } catch (error: any) {
        uToastRef.value.show({
            type: 'error',
            message: error[0].errorMessage
        })
        return
    }

    const subForm = Object.assign({}, form)

    if (!subForm.zdxz) {
        uToastRef.value.show({
            type: 'error',
            message: '请输入最低薪资!'
        })
        return
    }
    if (!contactList.length) {
        uToastRef.value.show({
            type: 'error',
            message: '紧急联系人不能为空!'
        })
        return
    }
    for (let item of contactList) {
        if (!item.mobileNo) {
            uToastRef.value.show({
                type: 'error',
                message: '请输入联系人手机号!'
            })
            return
        }
    }
    if (!degreeList.length) {
        uToastRef.value.show({
            type: 'error',
            message: '学习经历不能为空!'
        })
        return
    }
    for (let item of degreeList) {
        if (!item.school) {
            uToastRef.value.show({
                type: 'error',
                message: '请输入学校名称!'
            })
            return
        }
    }
    subForm.familyList = familyList
    subForm.contactList = contactList
    subForm.degreeList = degreeList
    subForm.jobList = jobList
    subForm.trainingList = trainingList
    subForm.questionArray = JSON.stringify(questionArray)

    if (confirmStatus) {
        subForm.confirmStatus = confirmStatus
        subForm.confirmTime = dayjs().format("YYYY-MM-DD HH:mm:ss")

        try {
            btnLoading.value = true
            let res = await uni.$u.http.put('/entry/user/register', subForm)
            btnLoading.value = false
            if (res.code === 200) {
                uToastRef.value.show({
                    type: 'success',
                    message: '上海宜侬生物科技有限公司欢迎您的加入!',
                    complete() {
                        if (confirmFlag.value) {
                            uni.reLaunch({ url: '/pages/index/index' })
                        } else {
                            navConfirmRef.value.open()
                        }
                    }
                })
            } else {
                uToastRef.value.show({
                    type: 'error',
                    message: res.msg,
                })
            }
        } catch (error) {
            btnLoading.value = false
        }
    } else {
        await nextTick()
        signatureRef.value.canvasToTempFilePath({
            async success(temp: any) {
                if (!temp.isEmpty) {
                    const fileRes = await uni.$u.http.upload('/common/upload', { filePath: temp.tempFilePath, name: 'file' })
                    if (fileRes.code === 200) {
                        subForm.signature = fileRes.url
                        try {
                            btnLoading.value = true
                            let res = await uni.$u.http.put('/entry/user/register', subForm)
                            btnLoading.value = false
                            if (res.code === 200) {
                                uToastRef.value.show({
                                    type: 'success',
                                    message: '提交成功',
                                    complete() {
                                        if (confirmFlag.value) {
                                            uni.reLaunch({ url: '/pages/index/index' })
                                        } else {
                                            navConfirmRef.value.open()
                                        }
                                    }
                                })
                            } else {
                                uToastRef.value.show({
                                    type: 'error',
                                    message: res.msg,
                                })
                            }
                        } catch (error) {
                            btnLoading.value = false
                        }
                    }
                } else {
                    uToastRef.value.show({
                        type: 'error',
                        message: '请签名确认!'
                    })
                    return
                }
            }
        })
    }
}
const addFamilyItem = () => {
    familyList.push({
        name: null,
        gx: null,
        age: null,
        gzdw: null,
        city: null,
    })
}
const delFamilyItem = (i: number) => {
    familyList.splice(i, 1)
}
const addContactItem = () => {
    contactList.push({
        name: null,
        mobileNo: null,
        gx: null,
        gzdw: null,
        city: null,
    })
}
const delContactItem = (i: number) => {
    contactList.splice(i, 1)
}
const addDegreeItem = () => {
    degreeList.push({
        startDate: null,
        endDate: null,
        school: null,
        professional: null,
        degree: null,
        remark: null,
    })
}
const delDegreeItem = (i: number) => {
    degreeList.splice(i, 1)
}
const addJobItem = () => {
    jobList.push({
        startDate: null,
        endDate: null,
        company: null,
        post: null,
        lzyy: null,
        certifier: null,
        certifierPhone: null,
        rsbmlxr: null,
        rsbmdh: null,
    })
}
const delJobItem = (i: number) => {
    jobList.splice(i, 1)
}
const addTrainingItem = () => {
    trainingList.push({
        startDate: null,
        endDate: null,
        institution: null,
        course: null,
        result: null,
    })
}
const delTrainingItem = (i: number) => {
    trainingList.splice(i, 1)
}
</script>
<style lang="scss" scoped>
.required {
    color: $uni-error;
    margin-right: 10rpx;
}

.avatar-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    .avatar-btn {
        margin-top: 10rpx;
        background-color: transparent;
        margin-bottom: 20rpx;
    }

    button[plain] {
        border: 0;
    }

    .tips {
        margin-top: 10rpx;
        font-size: 18rpx;
        color: $u-error;
    }
}

::v-deep .uni-table {
    .uni-table-tr {
        .uni-table-th {
            background-color: rgba(248, 248, 249, 1)
        }
    }
}

.bottom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: rgba(41, 121, 255, 1);
    color: #fff;
    text-align: center;
    height: 50px;
}

.tips-warpper {
    padding-bottom: 10px;

    .tips {
        color: $uni-error;
        font-size: $font-sm;
    }
}
</style>