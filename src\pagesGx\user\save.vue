<template>
  <view class="form-wrapper">
    <uni-forms :modelValue="form" :rules="rules" ref="formRef">
      <uni-forms-item required label="头像" name="avatar">
        <button
          plain
          class="avatar-wrapper"
          open-type="chooseAvatar"
          size="mini"
          @chooseavatar="onChooseAvatar"
        >
          <image v-if="form.avatar" class="avatar" :src="form.avatar" />
          <text v-else>请授权头像</text>
        </button>
      </uni-forms-item>
      <uni-forms-item required label="姓名" name="name">
        <uni-easyinput trim="both" v-model="form.name" />
      </uni-forms-item>
      <uni-forms-item required label="手机号" name="mobileNo">
        <uni-easyinput trim="both" v-model="form.mobileNo" type="number" />
      </uni-forms-item>
      <uni-forms-item required label="性别" name="sex">
        <uni-data-checkbox v-model="form.sex" :localdata="userSexOptions" />
      </uni-forms-item>
      <uni-forms-item required label="生日" name="birthday">
        <uni-datetime-picker type="date" v-model="form.birthday" />
      </uni-forms-item>
    </uni-forms>

    <button
      v-if="!readonly"
      type="primary"
      @click="submit"
      :loading="btnLoading"
      :disabled="btnLoading"
    >
      注 册
    </button>
  </view>

  <!-- #ifdef MP-WEIXIN -->
      <WsWxPrivacy id="privacy-popup" />
		<!-- #endif -->
  <up-loading-page :loading="loading" />
  <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { GxUser } from "@/types/gx/gxUser";
import { getDictData, navTo, resetForm, getDictLabel } from "@/utils/tools";
import { onLoad } from "@dcloudio/uni-app";
import { reactive, ref } from "vue";
import WsWxPrivacy from "@/components/ws-wx-privacy/ws-wx-privacy.vue";
import { useAuthStore } from "@/state/modules/user";
import { getAge } from "@/utils/tools";

const authStore = useAuthStore();
const form = reactive<GxUser>({
  id: undefined,
  avatar: "",
  mobileNo: "",
  name: "",
  sex: "",
  birthday: "",
  remark: "",
});
const rules = reactive({
  avatar: {
    rules: [
      {
        required: true,
        errorMessage: "请上传头像",
      },
    ],
  },
  name: {
    rules: [
      {
        required: true,
        errorMessage: "请输入姓名",
      },
    ],
  },
  mobile: {
    rules: [
      {
        required: true,
        errorMessage: "请输入手机号",
      },
      {
        pattern: "^1[3-9]\\d{9}$",
        errorMessage: "手机号格式不正确",
      },
    ],
  },
  sex: {
    rules: [
      {
        required: true,
        errorMessage: "请上传头像",
      },
    ],
  },
  birthday: {
    rules: [
      {
        required: true,
        errorMessage: "请选择生日",
      },
    ],
  },
});
const btnLoading = ref(false);
const loading = ref(false);
const formRef = ref();
const uToastRef = ref();
const userSexOptions = getDictData("sys_user_sex");
const readonly = ref(false);

onLoad(async (o: any) => {
  if (!uni.getStorageSync("dict_data")) {
    let dictRes = await uni.$u.http.get("/system/dict/all");
    uni.setStorageSync("dict_data", dictRes);
    uni.reLaunch({ url: "/pagesGx/user/save" });
  }
  if (o.readonly) {
    readonly.value = true;
  } else {
    readonly.value = false;
  }
  resetForm(form);
  uni.requirePrivacyAuthorize({
    success: () => {
      console.log('同意');
      // 用户同意授权
      // 继续小程序逻辑
    },
    fail: () => {
      console.log('拒绝');
    }, // 用户拒绝授权
    complete: () => {}
  })
  if (authStore.wxToken) {
    await initSelf();
  }
});

const onChooseAvatar = async (e: any) => {
  const uploadRes = await uni.$u.http.upload("/common/upload", {
    filePath: e.detail.avatarUrl,
    name: "file",
  });
  if (uploadRes.code === 200) {
    form.avatar = uploadRes.url;
  }
};
const initSelf = async () => {
  loading.value = true;
  const res = await uni.$u.http.get("/gx/user/getMyInfo");
  const data = res.data;
  if (res.code === 200 && data) {
    Object.assign(form, data);
  }
  loading.value = false;
};
const submit = async () => {
  const validateRes = await formRef.value.validate();

  const age = getAge(form.birthday);
  if (age < 18) {
    uToastRef.value.show({
      type: "error",
      message: "最小年龄不能低于18岁",
    });
    return;
  }
  const subForm = Object.assign({}, form);

  try {
    btnLoading.value = true;
    const res = await uni.$u.http.post("/gx/user/register", subForm);
    if (res.code === 200) {
      uToastRef.value.show({
        type: "success",
        message: "提交成功",
        complete() {
          uni.navigateBack({
            delta: 1,
            success() {
              uni.$emit("initGxUser");
            },
          });
        },
      });
    } else {
      uToastRef.value.show({
        type: "error",
        message: res.msg,
      });
    }
    btnLoading.value = false;
  } catch (error) {
    btnLoading.value = false;
  }
};
</script>
<style lang="scss" scoped>
.avatar-wrapper {
  background-color: transparent;

  .avatar {
    height: 36px;
    width: 36px;
  }

  button[plain] {
    border: 0;
  }
}

.bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: rgba(41, 121, 255, 1);
  color: #fff;
  text-align: center;
  height: 50px;
}
</style>
