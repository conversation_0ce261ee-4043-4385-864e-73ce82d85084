.page-wrapper {
    padding: 0 30rpx;
    background-color: #fff;
    min-height: 100%;
}

.table-tools {
	display: flex;
	justify-content: space-between;
	align-items: center;

	.btn-wrapper {
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}
}

.info-wrapper {
	padding: 20rpx;
	
	.plate-wrapper {
		background-color: $uni-bg-color;
		box-shadow: $uni-box-shadow;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		
		.plate-title {
			padding: 24rpx 20rpx;
			font-weight: 400;
			
			text {
				border-left: 10rpx solid $u-primary;
				padding-left: 15rpx;
			}

		}
		
		.plate-body {
			padding: 0rpx 20rpx 24rpx;
			
			.row {
				display: flex;
				align-items: center;
				min-height: 80rpx;
				border-bottom: 1rpx solid $u-border-color;
				
				.label {
					width: 130rpx;
					font-size: 28rpx;
					font-weight: 650;
					color: $u-main-color;
				}
				
				.value {
					flex: 1;
					color: $u-content-color;
				}
			}
		
			.list-wrapper {
				font-size: 28rpx;
				
				.title {
					font-weight: 650;
					color: $u-main-color;
					line-height: 60rpx;
				}
				
				.body {
					color: $u-content-color;
				}
			}
		}
	}
}

.search-wrapper {
	background-color: $uni-bg-color;
	
	.row {
		display: flex;
		align-items: center;
		min-height: 80rpx;
		border-bottom: 1rpx solid $u-border-color;
		
		.label {
			width: 130rpx;
			font-size: 28rpx;
			font-weight: 650;
			color: $u-main-color;
		}
		
		.value {
			flex: 1;
			color: $u-content-color;
		}
	}
	
	.btn-wrapper {
		padding-top: 15rpx;
		display: flex;
		flex-direction: row-reverse;
		
		.u-button {
			margin-right: 20rpx !important;
			height: 45rpx;
			width: 100rpx;
			margin: 0;
		}
	}
	
}

.list-wrapper {
	
	.btn-wrapper {
		display: flex;
		flex-direction: row-reverse;
		box-sizing: border-box;
		
		.u-button {
			margin-right: 20rpx !important;
		}
	}
	
}

.form-wrapper {
	padding: 20rpx;
	box-sizing: border-box;
	
	.plate-wrapper {
		background-color: $uni-bg-color;
		box-shadow: $uni-box-shadow;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		
		.plate-title {
			padding: 24rpx 20rpx;
			font-weight: 400;
			
			text {
				border-left: 10rpx solid $u-primary;
				padding-left: 15rpx;
			}
	
		}
		
		.plate-body {
			padding: 0rpx 20rpx 24rpx;
			
			.list-wrapper {
				font-size: 28rpx;
				
				.title {
					font-weight: 650;
					color: $u-main-color;
					line-height: 60rpx;
				}
				
				.body {
					color: $u-content-color;
				}
			}
		}
	}
	
}

.transparent-btn {
	background-color: transparent;
}

button[plain] {
	border: 0;
}

.mask{
    position: fixed;
    top:0;
    left:0;
    z-index:999;
    width:100%;
    height:100vh;
    background:rgba(0,0,0,0);
    display: flex;
    justify-content: center;
    align-items: center;
}

.cell-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx 0;
    border-bottom: 2rpx #b9b9b9 solid;

    .label {
        color: $uni-base-color;
        width: 130rpx;
        font-size: 28rpx;
        font-weight: 650;
    }

    .content {
        color: $uni-secondary-color;
    }
}