<template>
  <view>
    <uni-file-picker v-model="fileList" :file-mediatype="fileMediatype" :limit="limit" @select="select"
      @delete="deletePic">
      <button size="mini">选择文件</button>
    </uni-file-picker>
  </view>
</template>

<script setup lang="ts">
import type { File } from '@/types/file';
import { nextTick, reactive, watch } from 'vue';

let fileList = reactive<File[]>([])

const emit = defineEmits(['update:modelValue'])
const props = defineProps<{
  limit: {
    type: number,
    default: 9,
  },
  fileMediatype: {
    type: String,
    default: 'all',
  },
  modelValue: string,
  resize: {
    type: Function,
    default: () => {}
  }
}>()

watch(props, async (v) => {
  if (v && v.modelValue) {
    const array = JSON.parse(v.modelValue)
    fileList.length = 0
    fileList.push(...array)
  }
}, { immediate: true })

const deletePic = (event: any) => {
  fileList.splice(event.index, 1);
}

const select = async (event: any) => {
  uni.showLoading({
    title: '正在上传'
  });
  for (const temp of event.tempFiles) {
    const res = await uni.$u.http.upload('/common/uploadFile', {
      name: 'file',
      filePath: temp.url,
      formData: {
        fileName: temp.file.name,
      }
    })
    if (res.code === 200) {
      fileList.push({
        name: temp.file.name,
        extname: temp.extname,
        url: res.url,
      })
    }
  }
  emit('update:modelValue', JSON.stringify(fileList))
  props.resize()
  uni.hideLoading()
};

</script>

<style lang="scss" scoped>
/* 确保 uni-file-picker 有足够的空间显示 */
.uni-file-picker {
  width: 100%;
  /* 或者具体的宽度 */
  height: auto;
  /* 或者具体的高度 */
  overflow: visible;
  /* 避免内容被裁剪 */
}
</style>