<template>
    <view class="container">
      <scroll-view scroll-x class="scroll-view">
        <view 
          v-for="item in options" 
          :key="item.value"
          class="option"
          :class="{ 'active': modelValue === item.value }"
          @click="handleSelect(item.value)"
        >
          {{ item.label }}
        </view>
      </scroll-view>
    </view>
  </template>

<script setup lang="ts" >
import { reactive, watch } from 'vue';

const emit = defineEmits(['update:modelValue'])
const props = defineProps<{
    options?: {
      type: array,
      default: () => [],
    },
    modelValue: string,
}>()

const handleSelect = (value: string) => {
    emit('update:modelValue',value)
}

</script>
<style scoped>
.container {
  padding: 10px;
}

.scroll-view {
  white-space: nowrap;
  width: 100%;
}

.option {
  display: inline-block;
  padding: 8px 16px;
  margin-right: 10px;
  border-radius: 15px;
  background-color: #f5f5f5;
  color: #333;
}

.option.active {
  background-color: #007aff;
  color: white;
}

.option:last-child {
  margin-right: 0;
}
</style>