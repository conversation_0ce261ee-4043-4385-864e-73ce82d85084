<template>
    <view v-if="currentTab==='yl'">

    </view>
    <view v-if="currentTab==='gb'">

    </view>
    <view v-if="currentTab==='make'">

    </view>
    <view v-if="currentTab==='material'">

    </view>
    <view v-if="currentTab==='ingredients'">

    </view>

    <up-tabbar
        :value="currentTab"
        @change="tabChange"
    >
        <up-tabbar-item text="原料" icon="info-circle" name="yl" />
        <up-tabbar-item text="灌包" icon="clock" name="gb" />
        <up-tabbar-item text="配制" icon="facebook" name="make" />
        <up-tabbar-item text="包材" icon="moments" name="material" />
        <up-tabbar-item text="成分" icon="twitter-circle-fill" name="ingredients" />
    </up-tabbar>
</template>
<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';

const currentTab = ref('now')

onLoad(()=> {

})

const tabChange = (name: string) => {
    currentTab.value = name
}

</script>
<style lang="scss" scoped ></style>