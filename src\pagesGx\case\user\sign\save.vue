<template>
  <view class="form-wrapper">
    <uni-section title="方案" type="line">
      <image
        v-if="form.img"
        style="width: 710rpx"
        :src="form.img"
        mode="widthFix"
      />
      <view class="cell-wrapper">
        <view class="label">类型</view>
        <view class="content">{{ form.typeTreeLabel }}</view>
      </view>
      <view class="cell-wrapper">
        <view class="label">方案名称</view>
        <view class="content">{{ form.title }}</view>
      </view>
    </uni-section>

    <uni-section title="签到规则" type="line" />

    <uni-table border stripe emptyText="暂无更多数据">
      <uni-tr>
        <uni-th width="80" align="center">签到日期</uni-th>
        <uni-td width="160" align="center">
          {{ signDateArray.join(",") }}
        </uni-td>
      </uni-tr>
      <uni-tr>
        <uni-th width="120" align="center">签到时间段</uni-th>
        <uni-td width="160" align="center">
          <view v-for="(item, i) in signTimeArray" :key="i">
            {{ item.startTime }} - {{ item.endTime }}
          </view>
        </uni-td>
      </uni-tr>
    </uni-table>

    <uni-section title="签字" type="line" />

    <view @click="navTo('/pages/common/sign')">
      <image
        v-if="form.signature"
        :src="form.signature"
        mode="heightFix"
        style="height: 160rpx"
      />
      <view v-else style="height: 160rpx; text-align: center">签字区</view>
    </view>

    <button
      v-if="!readonly"
      @click="submit"
      size="mini"
      type="primary"
      :loading="btnLoading"
      :disabled="btnLoading"
    >
      提 交
    </button>
  </view>

  <up-loading-page :loading="loading" />
  <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { GxCaseUserSign } from "@/types/gx/gxCaseUserSign";
import { getDictLabel, resetForm, navTo } from "@/utils/tools";
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { reactive, ref, nextTick } from "vue";
import { useAuthStore } from "@/state/modules/user";

const authStore = useAuthStore();
const form = reactive<GxCaseUserSign>({
  id: undefined,
  signature: "",
  signDateArray: "",
  signTimeArray: "",
});
const btnLoading = ref(false);
const loading = ref(false);
const uToastRef = ref();
const readonly = ref(false);
const caseId = ref();
const signDateArray: any[] = reactive([]);
const signTimeArray: any[] = reactive([]);

onLoad(async (o: any) => {
  if (!uni.getStorageSync("dict_data")) {
    let dictRes = await uni.$u.http.get("/system/dict/all");
    uni.setStorageSync("dict_data", dictRes);
    uni.reLaunch({ url: "/pagesGx/case/user/sign/save?caseId=" + o.caseId });
  }
  if (o.readonly) {
    readonly.value = true;
  } else {
    readonly.value = false;
  }
  if (o.caseId) {
    caseId.value = o.caseId;
  } else {
    caseId.value = null;
  }
  uni.$on("initGxUser", initSelf);
  uni.$on("sign", sign);
  resetForm(form);
  signDateArray.length = 0;
  signTimeArray.length = 0;
  if (authStore.gxUser && authStore.gxUser.id) {
    await init();
  } else {
    if (authStore.wxToken) {
      await initSelf();
    }
  }
});
onUnload(async () => {
  uni.$off("initGxUser");
  uni.$off("sign");
});

const initSelf = async () => {
  loading.value = true;
  const res = await uni.$u.http.get("/gx/user/getMyInfo");
  let registerFlag = false; //是否已注册
  if (res.code === 200 && res.data) {
    if (res.data.avatar) {
      authStore.gxUser = res.data;

      await init();

      registerFlag = true;
    }
  }
  if (!registerFlag) {
    uni.navigateTo({
      url: "/pagesGx/user/save",
    });
  }
  loading.value = false;
};
const init = async () => {
  const params = {
    userId: authStore.gxUser.id,
    caseId: caseId.value,
  };
  const caseUserRes = await uni.$u.http.get("/gx/caseUser/getData", { params });
  if (caseUserRes.code === 200 && caseUserRes.data) {
    let logRes = await uni.$u.http.get(
      "/gx/caseUserSign/getByCaseUserId/" + caseUserRes.data.id
    );
    if (logRes.code === 200) {
      if (logRes.data) {
        if (logRes.data.signTime) {
          uToastRef.value.show({
            type: "error",
            message: "您已经签到过了",
            complete() {
              uni.redirectTo({ url: "/pagesGx/case/user/sign/index" });
            },
          });
          loading.value = false;
        }
        Object.assign(form, logRes.data);

        if (logRes.data.signDateArray) {
          const array = JSON.parse(logRes.data.signDateArray);
          signDateArray.length = 0;
          signDateArray.push(...array);
        }

        if (logRes.data.signTimeArray) {
          const array = JSON.parse(logRes.data.signTimeArray);
          signTimeArray.length = 0;
          signTimeArray.push(...array);
        }
      } else {
        uToastRef.value.show({
          type: "error",
          message: "当前时间段内没有可签到的方案",
          complete() {
            uni.redirectTo({ url: "/pagesGx/case/user/sign/index" });
          },
        });
      }
    }
  } else {
    const caseRes = await uni.$u.http.get('/gx/case/' + caseId.value)
    const data = caseRes.data
    if(caseRes.code === 200 && data) {
      if(data.typeTreeId === 1827) { //如果是感官测试,直接绑定通过.再次刷新页面
        bindUserPass()
      } else {
        uToastRef.value.show({
          type: "error",
          message: "未绑定测试方案!",
        });
        return;
      }
    }
  }
};
const bindUserPass = async() => {
    const userId = authStore.gxUser.id
    if(userId && caseId.value) {
        const params = {
          userId,
          caseId: caseId.value,
          signature: form.signature,
        }
        try {
            btnLoading.value = true
            let res = await uni.$u.http.post('/gx/caseUser/bindGg',params)
            btnLoading.value = false
            if(res.code === 200) {
                uToastRef.value.show({
                    type: 'success',
                    message: '提交成功',
                    complete() {
                      init()
                    }
                })
            } else {
                uToastRef.value.show({
                    type: 'error',
                    message: res.msg,
                    complete() {
                    }
                })
            }
        } catch (error) {
            btnLoading.value = false
        }
    }
}
const sign = async (v: string) => {
  form.signature = v;
};
const submit = async () => {
  await nextTick();
  if (!form.signature) {
    uToastRef.value.show({
      type: "error",
      message: "签名不能为空",
    });
    return;
  }
  const subForm = {
    id: form.id,
    signature: form.signature,
  };
  try {
    btnLoading.value = true;
    let res = await uni.$u.http.put("/gx/caseUserSign", subForm);
    btnLoading.value = false;
    if (res.code === 200) {
      uToastRef.value.show({
        type: "success",
        message: "提交成功",
        complete() {
          uni.$emit("refreshGxCaseUserSignList");
          uni.redirectTo({ url: "/pagesGx/case/user/sign/index" });
        },
      });
    } else {
      uToastRef.value.show({
        type: "error",
        message: res.msg,
      });
    }
  } catch (error) {
    btnLoading.value = false;
  }
};
</script>
<style lang="scss" scoped>
.bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: rgba(41, 121, 255, 1);
  color: #fff;
  text-align: center;
  height: 50px;
}
</style>
