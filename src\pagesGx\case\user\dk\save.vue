<template>
    <view class="form-wrapper" >
        <uni-section title="方案" type="line" >
            <image
                style="width: 710rpx;"
                :src="form.img"
                mode="widthFix"
            />
            <view class="cell-wrapper">
                <view class="label">类型</view>
                <view class="content">{{ form.typeTreeLabel}}</view>
            </view>
            <view class="cell-wrapper">
                <view class="label">方案名称</view>
                <view class="content">{{ form.title }}</view>
            </view>
        </uni-section>
        
        <uni-section title="打卡规则" type="line" />

        <uni-table border stripe emptyText="暂无更多数据" >
            <uni-tr>
                <uni-th width="80" align="center" >打卡日期</uni-th>
                <uni-td width="160" align="center" >
                    {{ form.startDate }} 至 {{ form.endDate }}
                </uni-td>
            </uni-tr>
            <uni-tr>
                <uni-th width="120" align="center" >打卡时间段</uni-th>
                <uni-td width="160" align="center" >
                    <view v-for="(item,i) in dkTimeArray" :key="i" >
                        {{ item.startTime }} - {{ item.endTime }}
                    </view>
                </uni-td>
            </uni-tr>
        </uni-table>

        <uni-section title="打卡" type="line" />
        
        <uni-forms :modelValue="form" :rules="rules" ref="formRef" >
            <uni-forms-item label="视频" required name="videos" >
                <ImageUpload v-model="form.videos" maxCount="1" accept="video" />
            </uni-forms-item>
        </uni-forms>

        <button v-if="!readonly" 
            @click="submit"
            type="primary"
            :loading="btnLoading" 
            :disabled="btnLoading" >提 交</button>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { GxCaseUserDk } from '@/types/gx/gxCaseUserDk';
import { resetForm,getDictLabel } from '@/utils/tools';
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';
import ImageUpload from '@/components/imageUpload.vue'

const form = reactive<GxCaseUserDk>({
    id: undefined,
    startDate: '',
    endDate: '',
    dkTimeArray: '',
    videos: '',
});
const dkTimeArray:any[] = reactive([])
const rules = reactive({
    videos: {
		rules: [
			{
                required: true, 
                errorMessage: '请上传视频',
            }
		]
	}, 
})
const btnLoading = ref(false)
const loading = ref(false)
const formRef = ref()
const uToastRef = ref()
const readonly = ref(false)

onLoad(async (o:any)=> {
    if(o.readonly) {
        readonly.value = true
    } else {
        readonly.value = false
    }
    resetForm(form)
    dkTimeArray.length = 0
    if(o.caseUserDkId) {
        loading.value = true
        let logRes = await uni.$u.http.get('/gx/caseUserDk/' + o.caseUserDkId)
        if(logRes.code === 200 && logRes.data) {
            Object.assign(form,logRes.data)

            if(logRes.data.dkTimeArray) {
            const array = JSON.parse(logRes.data.dkTimeArray)
            dkTimeArray.length = 0
            dkTimeArray.push(...array)
        }

        }
        loading.value = false
    }
})
const submit = async() => {
    const validateRes = await formRef.value.validate()
    const subForm = {
        id: form.id,
        videos: form.videos,
    }
    try {
        btnLoading.value = true
        let res = await uni.$u.http.put('/gx/caseUserDk',subForm)
        btnLoading.value = false
        if(res.code === 200) {
            uToastRef.value.show({
                type: 'success',
                message: '提交成功',
                complete() {
                    uni.$emit('refreshGxCaseUserDkList')
                    uni.navigateBack()
                }
            })
        } else {
            uToastRef.value.show({
                type: 'error',
                message: res.msg,
            })
        }
    } catch (error) {
        btnLoading.value = false
    }
}
</script>
<style lang="scss" scoped>

.bottom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
	background: rgba(41, 121, 255, 1);
    color: #fff;
    text-align: center;
    height: 50px;
}

</style>