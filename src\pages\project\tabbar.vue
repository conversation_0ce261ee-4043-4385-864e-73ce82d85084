<template>
    <view v-show="currentTab==='before'">
        <project-before />
    </view>
    <view v-show="currentTab==='current'">
        <project-current />
    </view>

    <up-tabbar
        :value="currentTab"
        @change="tabChange"
    >
        <up-tabbar-item text="立项前" name="before" >
            <template #active-icon>
                <text class="ali-icon ali-before" style="color: #2979ff;" />
            </template>
            <template #inactive-icon>
                <text class="ali-icon ali-before" />
            </template>
        </up-tabbar-item>
        <up-tabbar-item text="进行中" name="current" >
            <template #active-icon>
                <text class="ali-icon ali-jinhangzhong" style="color: #2979ff;" />
            </template>
            <template #inactive-icon>
                <text class="ali-icon ali-jinhangzhong" />
            </template>
        </up-tabbar-item>
    </up-tabbar>
</template>
<script setup lang="ts">
import ProjectBefore from './before/index.vue';
import ProjectCurrent from './current/index.vue';
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';

const currentTab = ref('before')

onLoad(()=> {

})

const tabChange = (name: string) => {
    currentTab.value = name
}

</script>
<style lang="scss" scoped ></style>