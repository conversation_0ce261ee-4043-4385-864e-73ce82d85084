// 打样单相关类型定义

// 打样单基本信息
export interface SampleOrder {
  id?: number
  sampleOrderCode: string // 打样单编号
  customerId?: number // 客户ID
  customerName?: string // 客户名称
  productName?: string // 产品名称
  completionStatus: number // 完成状态 0-未开始 1-进行中 2-已完成
  scheduledDate?: string // 排单日期
  endDate?: string // 最晚截至日期
  actualStartTime?: string // 实际开始时间
  actualFinishTime?: string // 实际完成时间
  laboratory?: string // 实验室编码
  userId?: number // 工程师ID
  nickName?: string // 工程师姓名
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  remark?: string // 备注
}

// 查询参数
export interface SampleOrderQuery {
  pageNum: number
  pageSize: number
  sampleOrderCode?: string // 打样单编号
  completionStatus?: number // 完成状态 - 确保是数字类型或undefined
  customerId?: number // 客户ID
  productName?: string // 产品名称
  laboratory?: string // 实验室编码
  beginDateRange?: string // 开始日期范围
  endDateRange?: string // 结束日期范围
  scheduledDate?: string // 排单日期
  actualStartTime?: string // 实际开始时间
  actualFinishTime?: string // 实际完成时间
  associationStatus?: number // 关联状态 1-已分配
  userId?: number // 工程师ID
  nickName?: string // 工程师姓名
  isOverdue?: number // 是否逾期 1-逾期
}

// 统计数据
export interface DashboardStats {
  total: number // 总任务数
  pending: number // 待处理
  inProgress: number // 进行中
  completed: number // 已完成
  overdue: number // 逾期
}
