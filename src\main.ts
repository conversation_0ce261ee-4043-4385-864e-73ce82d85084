import { createSSRApp } from 'vue'
import pinia from './state'
import App from './App.vue'
import router from './router'
import uviewPlus, {setConfig} from 'uview-plus'
import {initRequest} from './utils/request'
import dayjs from "dayjs"

export function createApp() {
  const app = createSSRApp(App)

  setConfig({
    config: {
    },
  })
  app.use(pinia)
  app.use(router)
  initRequest()
  app.use(uviewPlus)
  app.provide('dayjs', dayjs)
  return {
    app,
  }
}
