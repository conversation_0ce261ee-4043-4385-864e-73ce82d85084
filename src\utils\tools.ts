import type { Dict } from "@/types/dict";

export const filterParams = (obj: any) => {
  let _newPar: any = {};
  for (let key in obj) {
    //如果对象属性的值不为空，就保存该属性（这里我做了限制，如果属性的值为0，保存该属性。如果属性的值全部是空格，属于为空。
    if (
      (obj[key] === 0 || obj[key] === false || obj[key]) &&
      obj[key].toString().replace(/(^\s*)|(\s*$)/g, "") !== "" &&
      key !== "page" &&
      key !== "limit"
    ) {
      _newPar[key] = obj[key];
    }
  }
  return _newPar;
};

export const navTo = (url: string) => {
  uni.navigateTo({
    url,
  });
};

export const previewImg = (url?: string) => {
  if (url) {
    uni.previewImage({
      urls: [url],
    });
  }
};

export const getDictOptions = (dictType: string) => {
  const dict_data: any = uni.getStorageSync("dict_data");
  if (dict_data) {
    return dict_data[dictType];
  }
};

export const getDictData = (dictType: string) => {
  const array = getDictOptions(dictType);
  if (array && array[0]) {
    return array.map((i: Dict) => {
      return {
        value: i.dictValue,
        text: i.dictLabel,
      };
    });
  }
};

export const getDictLabel = (dictType: string, dictValue: String) => {
  if (dictValue) {
    const dict_data = uni.getStorageSync("dict_data");
    let array = dict_data[dictType].filter(
      (i: Dict) => i.dictValue === dictValue
    );
    if (Array.isArray(array) && array[0]) {
      return array[0].dictLabel;
    }
  }
};

export const getDictLabels = (dictType: string, dictValue: String) => {
  if (dictValue) {
    let vals = dictValue.split(",");
    if (vals.length > 0) {
      let labelArray = [];
      for (let v of vals) {
        labelArray.push(getDictLabel(dictType, v));
      }
      if (labelArray.length > 0) {
        return labelArray.join(",");
      }
    }
  }
};

export const getOptionsLabel = (array: Array<any>, value: string) => {
  const arr = array.filter((i) => i.value === value);
  if (arr && arr[0]) {
    return arr[0].text;
  }
};

export const resetForm = (obj: any) => {
  const keys = Object.keys(obj);
  for (const key of keys) {
    obj[key] = undefined;
  }
};

/**
 * 将总秒数转换成 分：秒
 * @param seconds - 秒
 */
export const secondToMinute = (seconds: number) => {
  const SECONDS_A_MINUTE = 60;
  const minuteNum = Math.floor(seconds / SECONDS_A_MINUTE);
  const second = seconds - minuteNum * SECONDS_A_MINUTE;
  return `${minuteNum}分${second}秒`;
};

/**
 * 根据日期计算年龄
 */
export const getAge = (val: any) => {
  let currentYear = new Date().getFullYear();
  let calculationYear = new Date(val).getFullYear();
  const wholeTime = currentYear + val.substring(4);
  const calculationAge = currentYear - calculationYear;

  //判断是否过了生日
  if (new Date().getTime() > new Date(wholeTime).getTime()) {
    return calculationAge;
  } else {
    return calculationAge - 1;
  }
};
