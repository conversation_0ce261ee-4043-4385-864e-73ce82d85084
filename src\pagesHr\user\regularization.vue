<template>
    <view class="form-wrapper">

        <uni-section title="试用期领导评价" type="line" />

        <uni-table border stripe emptyText="暂无更多数据">
            <uni-tr>
                <uni-th width="100" align="center">评价日期</uni-th>
                <uni-th width="100" align="center">评价人</uni-th>
                <uni-th width="100" align="center">评分</uni-th>
                <uni-th width="180" align="center">评价</uni-th>
                <uni-th width="160" align="center">执行方式</uni-th>
            </uni-tr>
            <uni-tr v-for="(item, i) in assessItemArray" :key="item.id">
                <uni-td align="center">{{ item.assessDate }}</uni-td>
                <uni-td align="center">{{ item.operatorName }}</uni-td>
                <uni-td align="center">{{ item.score }}</uni-td>
                <uni-td align="center">{{ item.appraise }}</uni-td>
                <uni-td align="center">{{ getOptionsLabel(executeOptions, item.execute) }}</uni-td>
            </uni-tr>
        </uni-table>

        <uni-section title="已完成项" type="line" />

        <uni-table border stripe emptyText="暂无更多数据">
            <uni-tr>
                <uni-th width="50" align="center">
                    <uni-icons type="plus" @click="addItem(finishedArray)" />
                </uni-th>
                <uni-th align="center">项目</uni-th>
            </uni-tr>
            <uni-tr v-for="(item, i) in finishedArray" :key="item.id">
                <uni-td align="center">
                    <uni-icons type="minus" @click="delItem(finishedArray, i)" />
                </uni-td>
                <uni-td align="center">
                    <uni-easyinput trim="both" v-model="item.label" />
                </uni-td>
            </uni-tr>
        </uni-table>

        <uni-section title="未完成项" type="line" />

        <uni-table border stripe emptyText="暂无更多数据">
            <uni-tr>
                <uni-th width="50" align="center">
                    <uni-icons type="plus" @click="addItem(todoArray)" />
                </uni-th>
                <uni-th align="center">项目</uni-th>
            </uni-tr>
            <uni-tr v-for="(item, i) in todoArray" :key="item.id">
                <uni-td align="center">
                    <uni-icons type="minus" @click="delItem(todoArray, i)" />
                </uni-td>
                <uni-td align="center">
                    <uni-easyinput trim="both" v-model="item.label" />
                </uni-td>
            </uni-tr>
        </uni-table>

        <uni-section title="提高和改善项" type="line" />

        <uni-table border stripe emptyText="暂无更多数据">
            <uni-tr>
                <uni-th width="50" align="center">
                    <uni-icons type="plus" @click="addItem(improveArray)" />
                </uni-th>
                <uni-th align="center">项目</uni-th>
            </uni-tr>
            <uni-tr v-for="(item, i) in improveArray" :key="item.id">
                <uni-td align="center">
                    <uni-icons type="minus" @click="delItem(improveArray, i)" />
                </uni-td>
                <uni-td align="center">
                    <uni-easyinput trim="both" v-model="item.label" />
                </uni-td>
            </uni-tr>
        </uni-table>

        <uni-section title="对公司的建议" type="line" />

        <uni-table border stripe emptyText="暂无更多数据">
            <uni-tr>
                <uni-th width="50" align="center">
                    <uni-icons type="plus" @click="addItem(suggestArray)" />
                </uni-th>
                <uni-th align="center">项目</uni-th>
            </uni-tr>
            <uni-tr v-for="(item, i) in suggestArray" :key="item.id">
                <uni-td align="center">
                    <uni-icons type="minus" @click="delItem(suggestArray, i)" />
                </uni-td>
                <uni-td align="center">
                    <uni-easyinput trim="both" v-model="item.label" />
                </uni-td>
            </uni-tr>
        </uni-table>

        <uni-forms :modelValue="form" :rules="rules" ref="formRef">
            <uni-forms-item label="试用期工作总结" required name="summary">
                <uni-easyinput trim="both" v-model="form.summary" type="textarea" />
            </uni-forms-item>
        </uni-forms>

        <button v-if="!readonly" @click="submit" type="primary" :loading="btnLoading" :disabled="btnLoading">提
            交</button>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { User } from '@/types/system/user';
import { getOptionsLabel, resetForm } from '@/utils/tools';
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';
import { useAuthStore } from '@/state/modules/user';

const authStore = useAuthStore();
const form = reactive<User>({
    userId: undefined,
    finishedArray: '',
    todoArray: '',
    improveArray: '',
    suggestArray: '',
    summary: '',
});
const rules = reactive({
    summary: {
        rules: [
            { required: true, errorMessage: '请输入试用期工作总结', },
        ]
    },
})
const finishedArray: any[] = reactive([])
const todoArray: any[] = reactive([])
const improveArray: any[] = reactive([])
const suggestArray: any[] = reactive([])
const assessItemArray: any[] = reactive([])
const btnLoading = ref(false)
const loading = ref(false)
const formRef = ref()
const uToastRef = ref()
const readonly = ref(false)
const executeOptions = [
    { text: '提前转正', value: '0' },
    { text: '试用期满转正', value: '1' },
    { text: '延长试用期转正', value: '2' },
    { text: '解除劳动关系', value: '3' },
]

onLoad(async (o: any) => {
    if (!uni.getStorageSync('dict_data')) {
        let dictRes = await uni.$u.http.get('/system/dict/all')
        uni.setStorageSync('dict_data', dictRes)
        uni.reLaunch({ url: '/pagesHr/user/regularization' })
    }
    if (o.readonly) {
        readonly.value = true
    } else {
        readonly.value = false
    }
    resetForm(form)
    if (authStore.wxToken) {
        await initSelf()
    }
})

const initSelf = async () => {
    loading.value = true
    const res = await uni.$u.http.get('/hr/user')
    if (res.code === 200 && res.data) {
        const data = res.data

        finishedArray.length = 0
        if (data.finishedArray) {
            const array = JSON.parse(data.finishedArray)
            if (array.length) {
                finishedArray.push(...array)
            } else {
                uToastRef.value.show({
                    type: 'error',
                    message: '已完成项不能为空',
                })
                return
            }
        } else {
            finishedArray.push({
                label: ''
            })
        }

        todoArray.length = 0
        if (data.todoArray) {
            const array = JSON.parse(data.todoArray)
            if (array.length) {
                todoArray.push(...array)
            }
        } else {
            todoArray.push({
                label: ''
            })
        }

        improveArray.length = 0
        if (data.improveArray) {
            const array = JSON.parse(data.improveArray)
            if (array.length) {
                improveArray.push(...array)
            } else {
                uToastRef.value.show({
                    type: 'error',
                    message: '提高和改善项不能为空',
                })
                return
            }
        } else {
            improveArray.push({
                label: ''
            })
        }

        suggestArray.length = 0
        if (data.suggestArray) {
            const array = JSON.parse(data.suggestArray)
            if (array.length) {
                suggestArray.push(...array)
            } else {
                uToastRef.value.show({
                    type: 'error',
                    message: '建议项不能为空',
                })
                return
            }
        } else {
            suggestArray.push({
                label: ''
            })
        }

        Object.assign(form, data)

        const params = { userId: data.userId }
        const assessItemList = await uni.$u.http.get('/user/assess/item/all', { params })
        assessItemArray.length = 0
        assessItemArray.push(...assessItemList)
    }
    loading.value = false
}
const submit = async () => {
    const validateRes = await formRef.value.validate()

    const subForm = Object.assign({}, form)

    subForm.finishedArray = JSON.stringify(finishedArray)
    subForm.todoArray = JSON.stringify(todoArray)
    subForm.improveArray = JSON.stringify(improveArray)
    subForm.suggestArray = JSON.stringify(suggestArray)

    try {
        btnLoading.value = true
        let res = await uni.$u.http.put('/hr/user', subForm)
        btnLoading.value = false
        if (res.code === 200) {
            uToastRef.value.show({
                type: 'success',
                message: '提交成功',
                complete() {
                    uni.reLaunch({ url: '/pages/index/index' })
                }
            })
        } else {
            uToastRef.value.show({
                type: 'error',
                message: res.msg,
            })
        }
    } catch (error) {
        btnLoading.value = false
    }

}
const addItem = (array: any) => {
    array.push({
        label: '',
    })
}
const delItem = (array: any, i: number) => {
    array.splice(i, 1)
}
</script>
<style lang="scss" scoped>
::v-deep .uni-table {
    .uni-table-tr {
        .uni-table-th {
            background-color: rgba(248, 248, 249, 1)
        }
    }
}
</style>