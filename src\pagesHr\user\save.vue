<template>
    <view class="form-wrapper">

        <view class="avatar-wrapper">
            <image style="width: 100px;height: 40px;"
                src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240521/1716270633460.png" />
            <button plain class="avatar-btn" @click="avatarClick" :loading="btnLoading">
                <up-avatar v-if="form.avatar" :src="form.avatar" :size="60" />
                <up-avatar v-else icon="plus" :size="60" />
            </button>
            <view class="tips" v-if="!form.avatar">请上传头像</view>
            <image style="width: 375px;height: 60px;"
                src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240521/1716270639502.png" />
        </view>

        <uni-forms :modelValue="form" :rules="rules" ref="formRef">
            <uni-forms-item label="姓名" name="nickName">
                <span style="line-height: 36px;">{{ form.nickName }}</span>
            </uni-forms-item>

            <uni-section title="个人基本信息" type="line" />

            <uni-forms-item label="性别" name="sex">
                <span style="line-height: 36px;">{{ getDictLabel('sys_user_sex', form.sex) }}</span>
            </uni-forms-item>
            <uni-forms-item label="邮箱" name="email">
                <span style="line-height: 36px;">{{ form.email }}</span>
            </uni-forms-item>
            <uni-forms-item label="手机号" name="phonenumber">
                <span style="line-height: 36px;">{{ form.phonenumber }}</span>
            </uni-forms-item>
            <uni-forms-item label="身份证号" name="cardNo">
                <span style="line-height: 36px;">{{ form.cardNo }}</span>
            </uni-forms-item>
            <uni-forms-item label="户籍地址" name="hukouHjdz">
                <span style="line-height: 36px;">{{ form.hukouHjdz }}</span>
            </uni-forms-item>
            <uni-forms-item label="现居住地址" name="hukouXjjdz">
                <span style="line-height: 36px;">{{ form.hukouXjjdz }}</span>
            </uni-forms-item>
            <uni-forms-item label="政治面貌" name="politicalFace">
                <span style="line-height: 36px;">{{ getDictLabel('sys_user_politicalFace', form.politicalFace) }}</span>
            </uni-forms-item>
            <uni-forms-item label="民族" name="ethnic">
                <span style="line-height: 36px;">{{ form.ethnic }}</span>
            </uni-forms-item>
            <uni-forms-item label="最高学历" name="degree">
                <span style="line-height: 36px;">{{ getDictLabel('sys_user_degree', form.degree) }}</span>
            </uni-forms-item>
            <uni-forms-item label="婚姻状况" name="hyzk">
                <span style="line-height: 36px;">{{ getDictLabel('sys_user_hyzk', form.hyzk) }}</span>
            </uni-forms-item>

            <uni-section title="身份证" type="line" />
            <uni-forms-item label="正面" name="cardBeforeImg">
                <ImageUpload v-model="form.cardBeforeImg" />
            </uni-forms-item>
            <uni-forms-item label="反面" name="cardAfterImg">
                <ImageUpload v-model="form.cardAfterImg" />
            </uni-forms-item>

            <uni-section title="家庭成员情况" type="line" />

            <uni-table border stripe emptyText="暂无更多数据">
                <uni-tr>
                    <!-- <uni-th width="50" align="center" >
                        <uni-icons type="plus" @click="addFamilyItem" />
                    </uni-th> -->
                    <uni-th align="center">姓名</uni-th>
                    <uni-th align="center">手机号</uni-th>
                    <uni-th align="center">关系</uni-th>
                    <uni-th align="center">年龄</uni-th>
                    <uni-th align="center">工作单位</uni-th>
                    <uni-th align="center">所在城市</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, i) in familyList" :key="item.id">
                    <!-- <uni-td align="center" >
                        <uni-icons type="minus" @click="delFamilyItem(i)" />
                    </uni-td> -->
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.name" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.mobileNo" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.gx" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.age" type="number" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.gzdw" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.city" />
                    </uni-td>
                </uni-tr>
            </uni-table>

            <uni-section title="紧急联系人(必要时需通知之亲友)" type="line" />

            <uni-table border stripe emptyText="暂无更多数据">
                <uni-tr>
                    <!-- <uni-th width="50" align="center" >
                        <uni-icons type="plus" @click="addContactItem" />
                    </uni-th> -->
                    <uni-th align="center">姓名</uni-th>
                    <uni-th align="center">关系</uni-th>
                    <uni-th align="center">联系电话</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, i) in contactList" :key="item.id">
                    <!-- <uni-td align="center" >
                        <uni-icons type="minus" @click="delContactItem(i)" />
                    </uni-td> -->
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.name" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.gx" />
                    </uni-td>
                    <uni-td align="center">
                        <uni-easyinput trim="both" v-model="item.mobileNo" />
                    </uni-td>
                </uni-tr>
            </uni-table>

            <uni-section title="学习经历(请从最高学历开始填写)" type="line" />

            <uni-table border stripe emptyText="暂无更多数据">
                <uni-tr>
                    <!-- <uni-th width="50" align="center" >
                        <uni-icons type="plus" @click="addDegreeItem" />
                    </uni-th> -->
                    <uni-th width="160" align="center">开始时间</uni-th>
                    <uni-th width="160" align="center">结束时间</uni-th>
                    <uni-th align="center">学校名称</uni-th>
                    <uni-th align="center">专业</uni-th>
                    <uni-th align="center">学历</uni-th>
                    <uni-th align="center">学位</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, i) in degreeList" :key="item.id">
                    <!-- <uni-td align="center" >
                        <uni-icons type="minus" @click="delDegreeItem(i)" />
                    </uni-td> -->
                    <uni-td align="center">
                        {{ item.startDate }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.endDate }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.school }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.professional }}
                    </uni-td>
                    <uni-td align="center">
                        <!-- <uni-data-select v-model="item.degree" :placement="(i === (degreeList.length-1))?'top':'bottom'" :localdata="degreeOptions" /> -->
                        {{ item.degree }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.remark }}
                    </uni-td>
                </uni-tr>
            </uni-table>

            <uni-section title="主要工作经历(请从最近一家开始填写)" type="line" />

            <uni-table border stripe emptyText="暂无更多数据">
                <uni-tr>
                    <!-- <uni-th width="50" align="center" >
                        <uni-icons type="plus" @click="addJobItem" />
                    </uni-th> -->
                    <uni-th width="160" align="center">开始时间</uni-th>
                    <uni-th width="160" align="center">结束时间</uni-th>
                    <uni-th align="center">工作单位名称</uni-th>
                    <uni-th align="center">职位</uni-th>
                    <uni-th align="center">离职原因</uni-th>
                    <uni-th align="center">证明人</uni-th>
                    <uni-th align="center">证明人联系电话</uni-th>
                    <uni-th align="center">人事部门联系人</uni-th>
                    <uni-th align="center">人事部固定电话</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, i) in jobList" :key="item.id">
                    <!-- <uni-td align="center" >
                        <uni-icons type="minus" @click="delJobItem(i)" />
                    </uni-td> -->
                    <uni-td align="center">
                        {{ item.startDate }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.endDate }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.company }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.post }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.lzyy }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.certifier }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.certifierPhone }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.rsbmlxr }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.rsbmdh }}
                    </uni-td>
                </uni-tr>
            </uni-table>

            <uni-section title="主要培训经历" type="line" />

            <uni-table border stripe emptyText="暂无更多数据">
                <uni-tr>
                    <!-- <uni-th width="50" align="center" >
                        <uni-icons type="plus" @click="addTrainingItem" />
                    </uni-th> -->
                    <uni-th width="160" align="center">开始时间</uni-th>
                    <uni-th width="160" align="center">结束时间</uni-th>
                    <uni-th align="center">培训机构</uni-th>
                    <uni-th align="center">培训课程</uni-th>
                    <uni-th width="100" align="center">培训证书</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, i) in trainingList" :key="item.id">
                    <!-- <uni-td align="center" >
                        <uni-icons type="minus" @click="delTrainingItem(i)" />
                    </uni-td> -->
                    <uni-td align="center">
                        {{ item.startDate }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.endDate }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.institution }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.course }}
                    </uni-td>
                    <uni-td align="center">
                        {{ item.result }}
                    </uni-td>
                </uni-tr>
            </uni-table>
        </uni-forms>

        <button v-if="!readonly" @click="dialogToggle()" type="primary" :loading="btnLoading" :disabled="btnLoading">提
            交</button>
    </view>

    <view>
        <uni-popup ref="submitConfirmRef" type="dialog">
            <uni-popup-dialog title="声 明" @confirm="submit">
                <template #default>
                    <text style="color: #e43d33;">我承诺以上信息均为真实记录，如有虚假成分，将导致录用后被解除劳动关系的后果，并承担相关法律责任。</text>
                </template>
            </uni-popup-dialog>
        </uni-popup>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { User } from '@/types/system/user';
import { getDictData, navTo, resetForm, getDictLabel } from '@/utils/tools';
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';
import ImageUpload from '@/components/imageUpload.vue'
import type { EntryUser } from '@/types/hr/entryUser';
import { useAuthStore } from '@/state/modules/user';

const authStore = useAuthStore();
const form = reactive<User>({
    userId: undefined,
    avatar: '',
    nickName: '',
    phonenumber: '',
    email: '',
    hyzk: '',
    ethnic: '',
    politicalFace: '',
    sex: '',
    degree: '',
    cardNo: '',
    hukouHjdz: '',
    hukouXjjdz: '',
    cardBeforeImg: '',
    cardAfterImg: '',
    directSuperior: undefined,
    fieldsJson: '',
    joinTime: '',
});
const rules = reactive({
})
const familyList: any[] = reactive([])
const contactList: any[] = reactive([])
const degreeList: any[] = reactive([])
const jobList: any[] = reactive([])
const trainingList: any[] = reactive([])
const btnLoading = ref(false)
const loading = ref(false)
const formRef = ref()
const uToastRef = ref()
const submitConfirmRef = ref()
const userSexOptions = getDictData('sys_user_sex')
const politicalFaceOptions = getDictData('sys_user_politicalFace')
const hjlbOptions = [
    { value: '0', text: '城镇(非农)' },
    { value: '1', text: '非城镇(农业)' },
    { value: '2', text: '本市征地户' },
]
const whetherOptions = [
    { value: '0', text: '否' },
    { value: '1', text: '是' },
]
const resourceOptions = [
    { value: '0', text: '网络' },
    { value: '1', text: '报刊杂志' },
    { value: '2', text: '校园' },
    { value: '3', text: '亲朋推荐' },
    { value: '4', text: '招聘会' },
    { value: '5', text: '猎头' },
    { value: '6', text: '职介所/劳务公司' },
]
const degreeOptions = getDictData('sys_user_degree')
const firstDegreeOptions = getDictData('sys_user_first_degree')
const hyzkOptions = getDictData('sys_user_hyzk')
const readonly = ref(false)

onLoad(async (o: any) => {
    if (!uni.getStorageSync('dict_data')) {
        let dictRes = await uni.$u.http.get('/system/dict/all')
        uni.setStorageSync('dict_data', dictRes)
        uni.reLaunch({ url: '/pagesHr/user/save' })
    }
    if (o.readonly) {
        readonly.value = true
    } else {
        readonly.value = false
    }
    resetForm(form)
    if (authStore.wxToken) {
        await initSelf()
    }
})

const dialogToggle = () => {
    submitConfirmRef.value.open()
}
const avatarClick = async () => {
    try {
        btnLoading.value = true
        const res = await uni.chooseImage({ count: 1 })
        if (res.errMsg === 'chooseImage:ok' && res.tempFilePaths) {
            const filePath = res.tempFilePaths[0]
            const uploadRes = await uni.$u.http.upload('/common/upload', { filePath, name: 'file' })
            if (uploadRes.code === 200) {
                form.avatar = uploadRes.url
            }
        }
        btnLoading.value = false
    } catch (error) {
        btnLoading.value = false
    }
}
const initSelf = async () => {
    loading.value = true
    const res = await uni.$u.http.get('/entry/user/getMyInfo')
    if (res.code === 200 && res.data) {
        const data = res.data
        if (data.phonenumber) {
            const userRes = await uni.$u.http.get('/user/getUserByPhoneNumber/' + data.phonenumber)
            if (userRes.code === 200 && userRes.data) {
                uToastRef.value.show({
                    type: 'success',
                    message: '用户信息已确认!',
                    complete() {
                        uni.reLaunch({ url: '/pages/index/index' })
                    }
                })
            } else {
                buildForm(data)
            }
        }
    }
    loading.value = false
}
const buildForm = (data: EntryUser) => {
    if (data.familyList) {
        familyList.length = 0
        familyList.push(...data.familyList)
    }
    if (data.contactList) {
        contactList.length = 0
        contactList.push(...data.contactList)
    }
    if (data.degreeList) {
        degreeList.length = 0
        degreeList.push(...data.degreeList)
    }
    if (data.jobList) {
        jobList.length = 0
        jobList.push(...data.jobList)
    }
    if (data.trainingList) {
        trainingList.length = 0
        trainingList.push(...data.trainingList)
    }

    form.entryUserId = data.userId
    form.deptId = data.deptId
    form.avatar = data.avatar
    form.nickName = data.userName
    form.phonenumber = data.phonenumber
    form.email = data.email
    form.hyzk = data.hyzk
    form.ethnic = data.hyzk
    form.politicalFace = data.politicalFace
    form.sex = data.sex
    form.degree = data.degree
    form.cardNo = data.cardNo
    form.hukouXjjdz = data.hukouXjjdz
    form.hukouHjdz = data.hukouHjdz
    form.directSuperior = data.directSuperior
    form.joinTime = data.serviceDate
}
const submit = async () => {
    const validateRes = await formRef.value.validate()
    const subForm = Object.assign({}, form)

    const fieldsJson = {
        jtcyDatas: familyList,
        jjlxrDatas: contactList,
        xyjlDatas: degreeList,
        gzjlDatas: jobList,
        pxjlDatas: trainingList,
    }

    subForm.fieldsJson = JSON.stringify(fieldsJson)

    try {
        btnLoading.value = true
        let res = await uni.$u.http.post('/user/register', subForm)
        btnLoading.value = false
        if (res.code === 200) {
            uToastRef.value.show({
                type: 'success',
                message: '提交成功',
                complete() {
                    uni.reLaunch({ url: '/pages/index/index' })
                }
            })
        } else {
            uToastRef.value.show({
                type: 'error',
                message: res.msg,
            })
        }
    } catch (error) {
        btnLoading.value = false
    }

}
const addFamilyItem = () => {
    familyList.push({
        name: null,
        gx: null,
        age: null,
        gzdw: null,
        city: null,
    })
}
const delFamilyItem = (i: number) => {
    familyList.splice(i, 1)
}
const addContactItem = () => {
    contactList.push({
        name: null,
        mobileNo: null,
        gx: null,
        gzdw: null,
        city: null,
    })
}
const delContactItem = (i: number) => {
    contactList.splice(i, 1)
}
const addDegreeItem = () => {
    degreeList.push({
        startDate: null,
        endDate: null,
        school: null,
        professional: null,
        degree: null,
        remark: null,
    })
}
const delDegreeItem = (i: number) => {
    degreeList.splice(i, 1)
}
const addJobItem = () => {
    jobList.push({
        startDate: null,
        endDate: null,
        company: null,
        post: null,
        lzyy: null,
        certifier: null,
        certifierPhone: null,
        rsbmlxr: null,
        rsbmdh: null,
    })
}
const delJobItem = (i: number) => {
    jobList.splice(i, 1)
}
const addTrainingItem = () => {
    trainingList.push({
        startDate: null,
        endDate: null,
        institution: null,
        course: null,
        result: null,
    })
}
const delTrainingItem = (i: number) => {
    trainingList.splice(i, 1)
}
</script>
<style lang="scss" scoped>
.avatar-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    .avatar-btn {
        margin-top: 10rpx;
        background-color: transparent;
        margin-bottom: 20rpx;
    }

    button[plain] {
        border: 0;
    }

    .tips {
        margin-top: 10rpx;
        font-size: 18rpx;
        color: $u-error;
    }
}

::v-deep .uni-table {
    .uni-table-tr {
        .uni-table-th {
            background-color: rgba(248, 248, 249, 1)
        }
    }
}

.bottom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: rgba(41, 121, 255, 1);
    color: #fff;
    text-align: center;
    height: 50px;
}
</style>