<template>
    <view class="form-wrapper">

        <uni-forms :modelValue="form" :rules="rules" ref="formRef" label-position="top">
            <uni-forms-item label="发送人" name="sendUser">
                {{ form.sendUser }}
            </uni-forms-item>
            <uni-forms-item label="发送时间" name="sendTime">
                {{ form.sendTime }}
            </uni-forms-item>

            <view v-for="(item, i) in formArray" :key="i">
                <view v-if="existsFlag(formArray, item)">
                    <view style="padding-bottom: 10rpx;">{{ item.label }}</view>
                    <view style="padding-bottom: 10rpx;">
                        <uni-easyinput v-if="item.type === 0" trim="both" v-model="item.value" />
                        <template v-if="item.type === 1">
                            <ScrollSelect v-if="form.typeTreeId === 1827" v-model="item.value"
                                :options="item.options" />
                            <uni-data-checkbox v-else v-model="item.value" :localdata="item.options" />
                        </template>
                        <uni-data-checkbox v-if="item.type === 2" multiple v-model="item.value"
                            :localdata="item.options" />
                    </view>
                </view>
            </view>

            <uni-section :title="p.label" type="line" v-for="(p, i) in productArray" :key="i">
                <uni-group mode="card" v-for="(item, i) in p.array" :key="i">
                    <view v-if="existsFlag(p.array, item)">
                        <view style="padding-bottom: 10rpx;">{{ item.label }}</view>
                        <view style="padding-bottom: 10rpx;">
                            <uni-easyinput v-if="item.type === 0" trim="both" v-model="item.value" />
                            <template v-if="item.type === 1">
                                <ScrollSelect v-if="form.typeTreeId === 1827" v-model="item.value"
                                    :options="item.options" />
                                <uni-data-checkbox v-else v-model="item.value" :localdata="item.options" />
                            </template>
                            <uni-data-checkbox v-if="item.type === 2" multiple v-model="item.value"
                                :localdata="item.options" />
                        </view>
                    </view>
                </uni-group>
            </uni-section>

            <view v-for="(item, i) in appendArray" :key="i">
                <view v-if="existsFlag(appendArray, item)">
                    <view style="padding-bottom: 10rpx;">{{ item.label }}</view>
                    <view style="padding-bottom: 10rpx;">
                        <uni-easyinput v-if="item.type === 0" trim="both" v-model="item.value" />
                        <template v-if="item.type === 1">
                            <ScrollSelect v-if="form.typeTreeId === 1827" v-model="item.value"
                                :options="item.options" />
                            <uni-data-checkbox v-else v-model="item.value" :localdata="item.options" />
                        </template>
                        <uni-data-checkbox v-if="item.type === 2" multiple v-model="item.value"
                            :localdata="item.options" />
                    </view>
                </view>
            </view>
        </uni-forms>

        <button v-if="!readonly" @click="submit" type="primary" :loading="btnLoading" :disabled="btnLoading">提
            交</button>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { GxCaseFormUser } from '@/types/gx/gxCaseFormUser';
import { resetForm, } from '@/utils/tools';
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref, inject } from 'vue';
import ScrollSelect from '@/components/scrollSelect.vue'

const form = reactive<GxCaseFormUser>({
    id: undefined,
    typeTreeLabel: '',
    type: '',
    sendUser: '',
    sendTime: '',
    replyTime: '',
    duration: undefined,
    formArray: '',
    productArray: '',
    appendArray: '',
});
const formArray: any[] = reactive([])
const productArray: any[] = reactive([])
const appendArray: any[] = reactive([])
const rules = reactive({
})
const btnLoading = ref(false)
const loading = ref(false)
const formRef = ref()
const uToastRef = ref()
const readonly = ref(false)
const startTime = ref('')
const dayjs: any = inject('dayjs')

onLoad(async (o: any) => {
    if (o.readonly) {
        readonly.value = true
    } else {
        readonly.value = false
    }
    resetForm(form)
    formArray.length = 0
    productArray.length = 0
    appendArray.length = 0
    if (o.id) {
        loading.value = true
        let res = await uni.$u.http.get('/gx/formUser/' + o.id)
        if (res.code === 200 && res.data) {
            Object.assign(form, res.data)
            console.log(res.data);

            if (res.data.formArray) {
                const array = JSON.parse(res.data.formArray)
                formArray.length = 0
                for (const item of array) {
                    if ([1, 2].includes(item.type)) {
                        for (const sub of item.options) {
                            sub.text = sub.label
                            sub.value = sub.id
                        }
                    }
                    formArray.push(item)
                }
                console.log(formArray);
            }

            if (res.data.productArray) {
                const array = JSON.parse(res.data.productArray)
                productArray.length = 0
                for (const p of array) {
                    for (const item of p.array) {
                        if ([1, 2].includes(item.type)) {
                            for (const sub of item.options) {
                                sub.text = sub.label
                                sub.value = sub.id
                            }
                        }
                    }
                    productArray.push(p)
                }
            }

            if (res.data.appendArray) {
                const array = JSON.parse(res.data.appendArray)
                appendArray.length = 0
                for (const item of array) {
                    if ([1, 2].includes(item.type)) {
                        for (const sub of item.options) {
                            sub.text = sub.label
                            sub.value = sub.id
                        }
                    }
                    appendArray.push(item)
                }
            }
        }
        loading.value = false
    }
    startTime.value = dayjs().format("YYYY-MM-DD HH:mm:ss")
})

const existsFlag = (formArray: any[], item: any) => {
    if (item.associationId) {
        const arr = formArray.filter(i => i.id === item.associationId)
        if (arr && arr[0]) {
            if (arr[0].value === item.associationValue) {
                return true
            }
        }
        return false
    } else {
        return true
    }
}
const submit = async () => {
    const validateRes = await formRef.value.validate()
    for (const item of formArray) {
        if (existsFlag(formArray, item)) {
            if (!item.value) {
                uToastRef.value.show({
                    type: 'error',
                    message: '请完整填写问卷',
                })
                return
            }
        }
    }
    for (const p of productArray) {
        for (const item of p.array) {
            if (existsFlag(p.array, item)) {
                if (!item.value) {
                    uToastRef.value.show({
                        type: 'error',
                        message: '请完整填写问卷',
                    })
                    return
                }
            }
        }
    }
    for (const item of appendArray) {
        if (existsFlag(appendArray, item)) {
            if (!item.value) {
                uToastRef.value.show({
                    type: 'error',
                    message: '请完整填写问卷',
                })
                return
            }
        }
    }
    const subForm: GxCaseFormUser = {
        id: form.id,
        formArray: JSON.stringify(formArray),
        productArray: JSON.stringify(productArray),
        appendArray: JSON.stringify(appendArray),
    }
    subForm.duration = dayjs(dayjs().format("YYYY-MM-DD HH:mm:ss")).diff(startTime.value, 'second')

    try {
        btnLoading.value = true
        let res = await uni.$u.http.put('/gx/formUser', subForm)
        btnLoading.value = false
        if (res.code === 200) {
            uToastRef.value.show({
                type: 'success',
                message: '提交成功',
                complete() {
                    uni.$emit('refreshGxCaseFormUser')
                    uni.navigateBack()
                }
            })
        } else {
            uToastRef.value.show({
                type: 'error',
                message: res.msg,
            })
        }
    } catch (error) {
        btnLoading.value = false
    }
}
</script>
<style lang="scss" scoped>
.bottom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: rgba(41, 121, 255, 1);
    color: #fff;
    text-align: center;
    height: 50px;
}
</style>