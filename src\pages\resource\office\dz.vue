<template>
    <view class="page-wrapper" >
        <uni-title type="h4" title="供应商" />
        <view >
            <text>
                {{ form.supplierName }}
            </text>
        </view>
        <uni-title type="h4" title="对账金额" />
        <view >
            <text>
                {{ form.money }}
            </text>
        </view>
    </view>

    <uni-section title="采购清单" type="line" />

    <uni-table border stripe emptyText="暂无更多数据" >
        <uni-tr>
            <uni-th width="150" align="center" >物资名称</uni-th>
            <uni-th width="100" >规格</uni-th>
            <uni-th width="60" >单位</uni-th>
            <uni-th width="80" >含税单价</uni-th>
            <uni-th width="80" >采购数量</uni-th>
            <uni-th width="120" >含税金额</uni-th>
        </uni-tr>
        <uni-tr v-for="item in dataArray" :key="item.id" >
            <uni-td>{{ item.materialName }}</uni-td>
            <uni-td>{{ item.spec }}</uni-td>
            <uni-td>{{ item.unit }}</uni-td>
            <uni-td>{{ item.price }}</uni-td>
            <uni-td>{{ item.nums }}</uni-td>
            <uni-td>{{ item.totalPrice }}</uni-td>
        </uni-tr>
    </uni-table>

    <view class="form-wrapper">
        <uni-forms :modelValue="form" :rules="rules" ref="formRef" >
            <uni-forms-item label="发票号" required name="fpCode">
                <uni-easyinput trim="both" v-model="form.fpCode" />
            </uni-forms-item>
            <uni-forms-item label="电子发票" required name="files" >
                <FileUpload v-model="form.files" />
            </uni-forms-item>
            <uni-forms-item label="发票金额" required name="fpMoney" >
                <uni-easyinput trim="both" v-model="form.fpMoney" type="number" />
            </uni-forms-item>
        </uni-forms>
    </view>
    
    <button
        :loading="btnLoading"
        :disabled="btnLoading"
        class="success"
        @click="fh"
    >   
        发 货
    </button>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { OfficeDz } from '@/types/resource/officeDz';
import type { PurchaseItem } from '@/types/process/purchaseItem';
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';
import FileUpload from '@/components/fileUpload.vue'

const btnLoading = ref(false)
const loading = ref(false)
const uToastRef = ref()
const form = reactive<OfficeDz>({
    id: undefined,
    supplierId: undefined,
    code: '',
    money: undefined,
    costMoney: undefined,
    supplierName: '',
    fpCode: '',
    files: '',
    qrCode: '',
    fpMoney: '',
});
const dataArray:PurchaseItem[] = reactive([])
const rules = reactive({
    fpCode: {
        rules: [
            {
                required: true, 
                errorMessage: '请填写发票号',
            },
        ]
    },
    files: {
        rules: [
            {
                required: true, 
                errorMessage: '请上传电子发票',
            },
        ]
    },
    fpMoney: {
        rules: [
            {
                required: true, 
                errorMessage: '请填写发票金额',
            },
        ]
    },
})
const formRef = ref()

onLoad(async (o:any)=> {
    await init(o.id)
})

const init = async(id: undefined) => {
    loading.value = true
    const res = await uni.$u.http.get('/resource/office/dz/' + id)
    const params = {officeDzId: id}
    const array = await uni.$u.http.get('/process/materialPurchaseUserItem/all', {params})
    dataArray.length = 0
    dataArray.push(...array)
    loading.value = false
    if(res.code === 200) {
        const data = res.data
        Object.assign(form, data)
    }
}

const fh = async () => {
    const validateRes = await formRef.value.validate()
    const params = {
        id: form.id,
        fpCode: form.fpCode,
        files: form.files,
        fpMoney: form.fpMoney,
    }
    try {
        btnLoading.value = true
        const res = await uni.$u.http.put('/resource/office/dz',params)
        btnLoading.value = false
        if(res.code === 200) {
            uToastRef.value.show({
				type: 'success',
				message: '提交成功',
				complete() {
					uni.reLaunch({
                        url: '/pages/index/index'
                    })
				}
			})
        } else {
            uToastRef.value.show({
				type: 'error',
				message: res.msg,
			})
        }
	} catch (error) {
        btnLoading.value = false
    }
}
</script>
<style lang="scss" scoped>
.btn-wrapper {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
}

.success {
    color: #fff;
    background-color: $uni-success;
}

.error {
    color: #fff;
    background-color: $uni-error;
}
</style>