<template>
    <view>
      <uni-file-picker 
        v-model="fileList" 
        file-mediatype="all"
        @select="select" 
        @delete="deletePic"
      >
        <button size="mini">选择文件</button>
      </uni-file-picker>
    </view>
</template>

<script setup lang="ts" >
import type { File } from '@/types/file';
import { nextTick, reactive, watch } from 'vue';

const emit = defineEmits(['update:modelValue'])
const props = defineProps<{
    mutiple?: boolean,
    fileList: Array<File>,
}>()

// watch(props,async (v)=>{
//   if(v && v.modelValue) {
//     const array = JSON.parse(v.modelValue)
//     fileList.length = 0
//     fileList.push(...array)
//   }
// },{immediate: true})

const deletePic = (event:any) => {
  props.fileList.splice(event.index, 1);
}

const select = async (event: any) => {
	uni.showLoading({
		title: '正在上传'
	});
  for(const temp of event.tempFiles) {
    const res = await uni.$u.http.upload('/common/uploadFile',{
      name: 'file',
      filePath: temp.url,
      formData: {
        fileName: temp.file.name,
      }
    })
    if(res.code === 200) {
      props.fileList.push({
        name: temp.file.name,
        extname: temp.extname,
        url: res.url,
      })
    }
  }
  uni.hideLoading()
};

</script>