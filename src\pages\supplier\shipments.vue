<template>
    <view class="page-wrapper" >
        <uni-title type="h4" title="供应商" />
        <view >
            <text>
                {{ form.supplierName }}
            </text>
        </view>
        <uni-title type="h4" title="付款类型" />
        <view >
            <text>
                <text v-if="form.payType === '0'" style="color: #2979ff;" >月结</text>
                <text v-if="form.payType === '1'" style="color: #18bc37;" >货到付款</text>
            </text>
        </view>
        <uni-title type="h4" title="发送人" />
        <view >
            <text>
                {{ form.createBy }}
            </text>
        </view>
        <uni-title type="h4" title="发送时间" />
        <view >
            <text>
                {{ form.createTime }}
            </text>
        </view>
    </view>

    <uni-section title="采购清单" type="line" />

    <uni-table border stripe emptyText="暂无更多数据" >
        <uni-tr>
            <uni-th width="150" align="center" >物资名称</uni-th>
            <uni-th width="100" >规格</uni-th>
            <uni-th width="60" >单位</uni-th>
            <!-- <uni-th width="60" >税率</uni-th>
            <uni-th width="80" >不含税单价</uni-th> -->
            <uni-th width="80" >含税单价</uni-th>
            <uni-th width="80" >采购数量</uni-th>
            <uni-th width="80" >发货数量</uni-th>
            <!-- <uni-th width="150" >不含税金额</uni-th> -->
            <uni-th width="120" >含税金额</uni-th>
        </uni-tr>
        <uni-tr v-for="item in dataArray" :key="item.id" >
            <uni-td>{{ item.materialName }}</uni-td>
            <uni-td>{{ item.spec }}</uni-td>
            <uni-td>{{ item.unit }}</uni-td>
            <!-- <uni-td>{{ item.rate * 100 }}% </uni-td> -->
            <!-- <uni-td>{{ item.costPrice }}</uni-td> -->
            <uni-td>{{ item.price }}</uni-td>
            <uni-td>{{ item.nums }}</uni-td>
            <uni-td>
                <uni-easyinput trim="both" v-model="item.fhNums" />
            </uni-td>
            <!-- <uni-td>{{ item.costMoney }}</uni-td> -->
            <uni-td><text v-if="item.fhNums" >{{ (item.fhNums * item.price).toFixed(2) }}</text></uni-td>
        </uni-tr>
    </uni-table>

    <view class="form-wrapper">
        <uni-forms :modelValue="form" :rules="rules" ref="formRef" >
            <template v-if="form.payType === '1'" >
                <uni-forms-item label="发票号" required name="fpCode">
                    <uni-easyinput trim="both" v-model="form.fpCode" />
                </uni-forms-item>
                <uni-forms-item label="电子发票" required name="files" >
                    <FileUpload v-model="form.files" />
                </uni-forms-item>
            </template>
        </uni-forms>
    </view>
    
    <button
        :loading="btnLoading"
        :disabled="btnLoading"
        class="success"
        @click="fh"
    >   
        发 货
    </button>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { SupplierPurchase } from '@/types/supplier/supplierPurchase';
import type { PurchaseItem } from '@/types/process/purchaseItem';
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';
import FileUpload from '@/components/fileUpload.vue'

const btnLoading = ref(false)
const loading = ref(false)
const uToastRef = ref()
const form = reactive<SupplierPurchase>({
    id: undefined,
    supplierName: '',
    createTime: '',
    createBy: '',
    fpCode: '',
    payType: '',
    files: '',
});
const dataArray:PurchaseItem[] = reactive([])
const rules = reactive({})
const formRef = ref()

onLoad(async (o:any)=> {
    await init(o.id)
})

const init = async(id: number) => {
    loading.value = true
    const res = await uni.$u.http.get('/supplier/purchase/' + id)
    const params = {supplierPurchaseId: id}
    const array = await uni.$u.http.get('/process/materialPurchaseUserItem/all', {params})
    dataArray.length = 0
    dataArray.push(...array)
    loading.value = false
    if(res.code === 200) {
        const data = res.data
        Object.assign(form, data)
    }
}

const fh = async () => {
    if(form.payType === '1') {//如果是 货到付款，需要上传发票号
        rules.fpCode = {
            rules: [
                {
                    required: true, 
                    errorMessage: '请填写发票号',
                },
            ]
        }
        rules.files = {
            rules: [
                {
                    required: true, 
                    errorMessage: '请上传电子发票',
                },
            ]
        }
    }
    const validateRes = await formRef.value.validate()
    const params = {
        id: form.id,
        itemList: dataArray.map(item => {
            return {
                id: item.id,
                fhNums: item.fhNums,
            }
        }),
    }
    if(form.payType === '1') {
        params.fpCode = form.fpCode
        params.files = form.files
    }
    try {
        btnLoading.value = true
        const res = await uni.$u.http.put('/supplier/purchase',params)
        btnLoading.value = false
        if(res.code === 200) {
            uToastRef.value.show({
				type: 'success',
				message: '提交成功',
				complete() {
					uni.reLaunch({
                        url: '/pages/index/index'
                    })
				}
			})
        } else {
            uToastRef.value.show({
				type: 'error',
				message: res.msg,
			})
        }
	} catch (error) {
        btnLoading.value = false
    }
}
</script>
<style lang="scss" scoped>
.btn-wrapper {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
}

.success {
    color: #fff;
    background-color: $uni-success;
}

.error {
    color: #fff;
    background-color: $uni-error;
}
</style>