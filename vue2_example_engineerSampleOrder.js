import request from '@/utils/request'

// 查询工程师打样单关联列表
export function listEngineerSampleOrder(query) {
  return request({
    url: '/software/engineerSampleOrder/list',
    method: 'get',
    params: query
  })
}

// 查询工程师打样单关联列表
export function listEngineerSampleOrderByBusiness(query) {
  return request({
    url: '/software/engineerSampleOrder/listByBusiness',
    method: 'get',
    params: query
  })
}

// 查询工程师打样单关联详细
export function getEngineerSampleOrder(id) {
  return request({
    url: '/software/engineerSampleOrder/' + id,
    method: 'get'
  })
}



// 查询工程师打样单关联详细
export function getExecutionByOrderInfo(id) {
  return request({
    url: '/software/engineerSampleOrder/executionInfo/' + id,
    method: 'get'
  })
}

// 新增工程师打样单关联
export function addEngineerSampleOrder(data) {
  return request({
    url: '/software/engineerSampleOrder',
    method: 'post',
    data: data
  })
}

// 修改工程师打样单关联
export function updateEngineerSampleOrder(data) {
  return request({
    url: '/software/engineerSampleOrder',
    method: 'put',
    data: data
  })
}


// 删除工程师打样单关联
export function delEngineerSampleOrder(id) {
  return request({
    url: '/software/engineerSampleOrder/' + id,
    method: 'delete'
  })
}

// 导出工程师打样单关联
export function exportEngineerSampleOrder(query) {
  return request({
    url: '/software/engineerSampleOrder/export',
    method: 'get',
    params: query
  })
}

// 更新工程师打样单状态
export function updateSampleOrderStatus(data) {
  return request({
    url: '/software/engineerSampleOrder/updateStatus',
    method: 'post',
    data: data
  })
}

// 获取看板统计数据
export function getDashboardStats(query) {
  return request({
    url: '/software/engineerSampleOrder/dashboardStats',
    method: 'get',
    params: query
  })
}

// 获取看板统计数据
export function getDashboardStatsBusiness(query) {
  return request({
    url: '/software/engineerSampleOrder/dashboardStatsBusiness',
    method: 'get',
    params: query
  })
}

// 获取研发部门子部门列表
export function getResearchDepartments() {
  return request({
    url: '/software/engineerSampleOrder/researchDepartments',
    method: 'get'
  })
}

// 更改工程师
export function changeEngineer(data) {
  return request({
    url: '/software/engineerSampleOrder/changeEngineer',
    method: 'post',
    data: data
  })
}

// 获取指定难度和类别的工程师列表
export function getEngineersByDifficultyLevel(query) {
  return request({
    url: '/software/engineerSampleOrder/engineersByDifficultyLevel',
    method: 'get',
    params: query
  })
}

// 研发部门的工程师列表
export function getResearchDepartmentsUser() {
  return request({
    url: '/software/engineerSampleOrder/researchDepartmentsUser',
    method: 'get'
  })
}

// 获取工程师工时数据
export function getEngineerWorkHours(query) {
  return request({
    url: '/software/engineerWorkRecord/dashboard/workHours',
    method: 'get',
    params: query
  })
}

// 获取未分配工单列表
export function getUnassignedOrders(query) {
  return request({
    url: '/software/engineerSampleOrder/dashboard/unassigned',
    method: 'get',
    params: query
  })
}

// 分配工单给工程师
export function assignOrderToEngineer(data) {
  return request({
    url: '/software/engineerSampleOrder/assign',
    method: 'post',
    data: data
  })
}

// 退单
export function returnOrder(id, reason) {
  return request({
    url: '/software/engineerSampleOrder/return/' + id,
    method: 'put',
    data: { reason }
  })
}

// 延期工单
// export function delayOrder(id, data) {
//   return request({
//     url: '/software/engineerSampleOrder/delay/' + id,
//     method: 'put',
//     data: data
//   })
// }

// ==================== 批次管理相关API ====================
// 根据工程师打样单ID查询批次列表
export function getBatchesByOrderId(engineerSampleOrderId) {
  return request({
    url: '/software/sampleOrderBatch/listByOrderId/' + engineerSampleOrderId,
    method: 'get'
  })
}

// 查询当前批次
export function getCurrentBatch(engineerSampleOrderId) {
  return request({
    url: '/software/sampleOrderBatch/currentBatch/' + engineerSampleOrderId,
    method: 'get'
  })
}

// 开始新批次
export function startNewBatch(engineerSampleOrderId, remark) {
  return request({
    url: '/software/sampleOrderBatch/startBatch',
    method: 'post',
    params: { engineerSampleOrderId, remark }
  })
}

// 结束当前批次
export function finishCurrentBatch(engineerSampleOrderId, qualityEvaluation, remark) {
  return request({
    url: '/software/sampleOrderBatch/finishBatch',
    method: 'post',
    params: { engineerSampleOrderId, qualityEvaluation, remark }
  })
}

// 为批次添加单个实验记录
export function addExperimentToBatch(experimentRecord) {
  return request({
    url: '/software/sampleOrderBatch/addExperiment',
    method: 'post',
    data: experimentRecord
  })
}

// 查询批次的实验记录
export function getExperimentsByBatchId(batchId) {
  return request({
    url: '/software/sampleOrderBatch/experiments/' + batchId,
    method: 'get'
  })
}

// 结束指定批次
// export function finishBatchById(batchId, qualityEvaluation, remark) {
//   return request({
//     url: '/software/sampleOrderBatch/finishBatchById',
//     method: 'post',
//     params: { batchId, qualityEvaluation, remark }
//   })
// }

// 检查是否有进行中的批次
// export function hasCurrentBatch(engineerSampleOrderId) {
//   return request({
//     url: '/software/sampleOrderBatch/hasCurrentBatch/' + engineerSampleOrderId,
//     method: 'get'
//   })
// }

// 获取总批次数
// export function getTotalBatchCount(engineerSampleOrderId) {
//   return request({
//     url: '/software/sampleOrderBatch/totalBatchCount/' + engineerSampleOrderId,
//     method: 'get'
//   })
// }

// ==================== 新增仪表板API ====================

// 获取组别项目汇总数据
export function getGroupSummary(query) {
  return request({
    url: '/software/engineerSampleOrder/dashboard/groupSummary',
    method: 'get',
    params: query
  })
}

// 获取工程师项目汇总数据
export function getEngineerSummary(query) {
  return request({
    url: '/software/engineerSampleOrder/dashboard/engineerSummary',
    method: 'get',
    params: query
  })
}

// 获取打样进度明细数据
export function getSampleDetail(query) {
  return request({
    url: '/software/engineerSampleOrder/dashboard/sampleDetail',
    method: 'get',
    params: query
  })
}

// 获取报警信息数据
export function getAlertInfo(query) {
  return request({
    url: '/software/engineerSampleOrder/dashboard/alertInfo',
    method: 'get',
    params: query
  })
}

