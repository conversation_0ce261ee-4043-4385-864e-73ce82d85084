<template>
    <view >
        <uni-section class="mb-10" title="用户服务协议" >1</uni-section>
        <uni-section class="mb-10" title="隐私政策" >2</uni-section>
    </view>
</template>
<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';
onLoad((o) => {
    if(o && o.type) {
        console.log(o.type)
    }
});
</script>
<style lang="sass" scoped>

</style>