import type { Router } from "uni-mini-router";
import { useAuthStore } from "@/state/modules/user";

const whiteList = [
  "login",
  "hrApply",
  "hrQa",
  "gxUserSave",
  "gxCaseBind",
  "gxCaseUserDk",
  "gxCaseUserDkSave",
  "gxCaseUserSignSave",
  "gxCaseUserSign",
  "sign",
  "gxCaseform",
  "gxCaseformSave",
  "visitSave",
  "images",
  "supplierShipments",
  "officeDz",
];

export function userRouterGuard(router: Router) {
  router.beforeEach((to, from, next) => {
    // next入参 false 以取消导航
    const authStore = useAuthStore();
    // 判断是否需要登录
    if (authStore.isLogin) {
      // 如果登录了就放行
      next();
    } else {
      if (whiteList.includes(to.name as string)) {
        // 在免登录白名单，直接进入
        next();
      } else {
        // 判断不是白名单 还没有token值 直接进去登录页面页面
        next({
          name: "login",
        });
      }
    }

    // next();
  });

  router.afterEach((to) => {
    const authStore = useAuthStore();

    if (!authStore.isLogin && to && to.name !== "login") {
      // 如果没有登录且目标路由不是登录页面则跳转到登录页面

      if (whiteList.includes(to.name as string)) {
        // 在免登录白名单，直接进入
      } else {
        router.push({
          name: "login",
          params: { ...to.query },
        });
      }
    } else if (authStore.isLogin && to && to.name === "login") {
      // 如果已经登录且目标页面是登录页面则跳转至首页
      router.replaceAll({ name: "login" });
    }
  });
}
