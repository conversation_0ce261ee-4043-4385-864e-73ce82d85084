@import 'uview-plus/theme.scss';
@import "@dcloudio/uni-ui/lib/uni-scss/variables.scss";

$u-type-primary: #2979ff;
$u-type-info-dark: #82848a;

$uni-bg-color: #fff;
$uni-bg-grey: #F3F4F6;

$uni-font-label: #606266;

$uni-box-shadow: 0 5px 15px -5px rgba(0,0,0,.5);

/* 页面左右间距 */
$page-row-spacing: 30rpx;
$page-color-base: #f8f8f8;
$page-color-light: #f8f6fc;
$base-color: #fa436a;

/* 文字尺寸 */
$font-sm: 24rpx;
$font-base: 28rpx;
$font-lg: 32rpx;
/*文字颜色*/
$font-color-dark: #303133;
$font-color-base: #606266;
$font-color-light: #909399;
$font-color-disabled: #C0C4CC;
$font-color-spec: #4399fc;
/* 边框颜色 */
$border-color-dark: #DCDFE6;
$border-color-base: #E4E7ED;
$border-color-light: #EBEEF5;
/* 图片加载中颜色 */
$image-bg-color: #eee;
/* 行为相关颜色 */
$uni-color-primary:#fa436a;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;
