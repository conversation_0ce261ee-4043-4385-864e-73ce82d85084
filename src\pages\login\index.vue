<template>
	<view class="logo-wrapper" >
		<image
			style="width: 200rpx;"
			src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240521/1716270636193.png"
			mode="widthFix"
		/>
	</view>
	
  	<view class="form-wrapper" >
		<uni-forms :modelValue="form" :rules="rules" ref="formRef" >
			<uni-forms-item name="userName" label="用户名" label-width="60px" >
				<uni-easyinput v-model="form.userName" >
					<template #right>
					</template>
				</uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="password" label="密码" label-width="60px" >
				<uni-easyinput v-model="form.password" type="password" />
			</uni-forms-item>
		</uni-forms>
		<button @click="submit" 
		class="submit-btn"
		:loading="btnLoading" 
		:disabled="btnLoading" 
			>登 录
		</button>
		<view class="tips">账号密码同sap</view>
	</view>

	<view style="height: 60rpx;" />

	<view style="padding: 20rpx;" v-if="authStore.gxUser && authStore.gxUser.id" >
		<uni-row >
			<uni-col span="8" >
				<uni-tag :inverted="true" text="功效打卡" type="primary" @click="navTo('/pagesGx/case/user/dk/index')" />
			</uni-col>
			<uni-col span="8" >
				<uni-tag :inverted="true" text="消费者调研" type="primary" @click="navTo('/pagesGx/case/form/index')" />
			</uni-col>
			<uni-col span="8" >

			</uni-col>
		</uni-row>
	</view>
	<view style="padding: 20rpx;" v-else >
		<uni-tag :inverted="true" text="授权功效" type="primary" @click="initSelf" />
	</view>
	
	<up-toast ref="uToastRef" />
</template>

<script lang="ts" setup>

import { useAuthStore } from '@/state/modules/user';
import { navTo } from '@/utils/tools';
import { reactive, ref } from 'vue';
import { onLoad,onUnload } from '@dcloudio/uni-app';

const authStore = useAuthStore();
const form = reactive({
  userName: null,
  password: null,
});
const rules = reactive({
	userName: {
		rules: [
			{required: true, errorMessage: '请输入用户名',},
		]
	},
	password: {
		rules: [
			{required: true, errorMessage: '请输入密码',},
		]
	},
})
const btnLoading = ref(false)
const loading = ref(false)
const formRef = ref()
const uToastRef = ref()

onLoad(async ()=> {
	uni.$on('initGxUser', initSelf);
})
onUnload(async() => {
	uni.$off('initGxUser')
})

const initSelf = async () => {
    loading.value = true
    const res = await uni.$u.http.get('/gx/user/getMyInfo')
    let registerFlag = false //是否已注册
    if(res.code === 200 && res.data) {
        if(res.data.avatar) {
            authStore.gxUser = res.data
            registerFlag = true
        }
    }
    if(!registerFlag) {
        uni.navigateTo({
            url: '/pagesGx/user/save'
        })
    }
    loading.value = false
}
const submit = async() => {
	try {
		const ress = await formRef.value.validate()
		btnLoading.value = true
		const res = await authStore.login(form)
		btnLoading.value = false
		if(authStore.isLogin) {
			uToastRef.value.show({
				type: 'success',
				message: '登录成功',
				complete() {
					uni.reLaunch({url: '/pages/index/index'})
				}
			})
		} else {
			uToastRef.value.show({
				type: 'error',
				message: res.msg,
			})
		}
	} catch (error) {
		btnLoading.value = false
	}
}

</script>

<style lang="scss" scoped>

.logo-wrapper {
	margin: 200rpx 0 100rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.form-wrapper {
	padding: 0 50rpx;
	box-sizing: border-box;
}

.submit-btn {
	display: flex;
	justify-content: center;
	align-items: center;
	color: #FFF;
	font-size: 30rpx;
	white-space:nowrap;
	overflow: hidden;
	width:601rpx;
	height:100rpx;
	background:linear-gradient(to right, rgba(0,0,0,0.7), rgba(0,0,0,0.6));
	box-shadow:0rpx 0rpx 13rpx 0rpx rgba(164,217,228,0.4);
	border-radius: 50rpx;
	border: none ;
	box-shadow: 0 0 60rpx 0 rgba(0,0,0,.2) ;
	transition: all 0.4s cubic-bezier(.57,.19,.51,.95);
}

.img-wrapper {
	display: flex;
	justify-content: center;
	align-items: center;
}

.tips {
	text-align: center;
	color: #999;
	font-size: 24rpx;
	margin-top: 20rpx;
}

</style>