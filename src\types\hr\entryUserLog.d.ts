export type EntryUserLog = {
    id?: number,
    jbSz?: number,
    zrYs?: number,
    gwZs?: number,
    ysJl?: number,
    ldNl?: number,
    xzNl?: number,
    gh?: number,
    xzYq?: number,
    xxNl?: number,
    qxGl?: number,
    score?: number,
    level?: string,
    result?: string,
    appraise?: string,
    operatorId?: number,
    step?: string,
    interViewDate?: string,
    userId?: number,
    avatar?: string,
    userName?: string,
    applicationDate?: string,
    applicationPosition?: string,
    onDutyDate?: string,
    sex?: string,
    birthday?: string,
    age?: number,
    email?: string,
    phonenumber?: string,
    gddh?: string,
    cardNo?: string,
    hukouHjdz?: string,
    birthplace?: string,
    hukouXjjdz?: string,
    ethnic?: string,
    hjlb?: string,
    politicalFace?: string,
    weight?: number,
    height?: number,
    degree?: string,
    firstDegree?: string,
    xuewei?: string,
    yzzs?: string,
    wykynl?: string,
    hyzk?: string,
    znzkMan?: string,
    znzkWoman?: string,
    zdxz?: number,
    zdxzOther?: string,
    zhyjxz?: string,
    zhyjxzHj?: number,
    ahtc?: string,
    sfshjzz?: string,
    sffz?: string,
    sfhy?: string,
    sfzb?: string,
    sfzbOther?: string,
    sfqs?: string,
    sfqsOther?: string,
    sfgl?: string,
    sfglOther?: string,
    sfglqs?: string,
    sfglqsOther?: string,
    zpxx?: string,
    zpxxOther?: string,
}