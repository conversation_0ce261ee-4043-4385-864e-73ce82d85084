<template>
    <view class="form-wrapper" >
        <uni-forms :modelValue="form" :rules="rules" ref="formRef" label-width="120" >
            <uni-forms-item label="客户姓名" required name="name" >
                <uni-easyinput trim="both" v-model="form.name" />
            </uni-forms-item>
            <uni-forms-item label="手机号" required name="phone" >
                <uni-easyinput trim="both" v-model="form.phone" />
            </uni-forms-item>
            <uni-forms-item label="随行人数" required name="companionCount" >
                <uni-easyinput trim="both" v-model="form.companionCount" type="number" />
            </uni-forms-item>
            <uni-forms-item label="公司" required name="companyName" >
                <uni-easyinput trim="both" v-model="form.companyName" />
            </uni-forms-item>
            <uni-forms-item label="预约访问时间" required name="scheduleTime" >
                <uni-datetime-picker type="date" v-model="form.scheduleTime" />
            </uni-forms-item>
            <uni-forms-item label="实际到达时间" name="checkinTime" >
                {{ form.checkinTime }}
            </uni-forms-item>
            <uni-forms-item label="车辆信息" name="carInfo" >
                <uni-easyinput trim="both" v-model="form.carInfo" type="textarea" />
            </uni-forms-item>
            <uni-forms-item label="访问事由" name="purpose" >
                <uni-easyinput trim="both" v-model="form.purpose" type="textarea" />
            </uni-forms-item>
            <uni-forms-item label="备注" name="remark" >
                <uni-easyinput trim="both" v-model="form.remark" type="textarea" />
            </uni-forms-item>
        </uni-forms>
        <button v-if="!readonly" @click="submit" type="primary" :loading="btnLoading" :disabled="btnLoading" >提 交</button>
    </view>

    <up-loading-page :loading="loading" />
    <up-toast ref="uToastRef" />
</template>
<script lang="ts" setup>
import type { CustomerVisit } from '@/types/customer/customerVisit';
import { onLoad } from '@dcloudio/uni-app';
import { reactive, ref } from 'vue';

const form = reactive<CustomerVisit>({
    id: undefined,
    userId: undefined,
    name: '',
    phone: '',
    companionCount: undefined,
    companyName: '',
    qrCode: '',
    purpose: '',
    scheduleTime: '',
    checkinTime: '',
    checkoutTime: '',
    carInfo: '',
    remark: '',
});
const rules = reactive({
	name: {
		rules: [
			{required: true, errorMessage: '请输入客户名称',},
		]
	},
    phone: {
		rules: [
			{required: true, errorMessage: '请输入手机号',},
		]
	},
    companionCount: {
		rules: [
			{required: true, errorMessage: '请输入随行人数',},
		]
	},
    companyName: {
		rules: [
			{required: true, errorMessage: '请输入公司',},
		]
	},
    scheduleTime: {
        rules: [
            {required: true, errorMessage: '请选择预约访问时间',},
        ]
    },
})
const btnLoading = ref(false)
const loading = ref(false)
const formRef = ref()
const uToastRef = ref()
const readonly = ref(false)

onLoad(async (o:any)=> {
    if(o.id) {
        await init(o.id)
    }
    if(o.readonly) {
        readonly.value = true
    } else {
        readonly.value = false
    }
})

const init = async (id: Number) => {
    loading.value = true
    const res = await uni.$u.http.get('/customer/visit/' + id)
    loading.value = false
    if(res.code === 200) {
        const data = res.data
        Object.assign(form, data)
    }
}
const submit = async() => {
    const validateRes = await formRef.value.validate()
    const subForm = Object.assign({},form)
    let res;
    try {
        btnLoading.value = true
        if (subForm.id != null) {
            res = await uni.$u.http.put('/customer/visit',subForm)
        } else {
            res = await uni.$u.http.post('/customer/visit',subForm)
        }
        btnLoading.value = false
        if(res.code === 200) {
            uToastRef.value.show({
				type: 'success',
				message: '提交成功',
				complete() {
                    uni.$emit('refreshCustomerVisit')
					uni.navigateBack({
						delta: 1
					})
				}
			})
        } else {
            uToastRef.value.show({
				type: 'error',
				message: res.msg,
			})
        }
	} catch (error) {
        btnLoading.value = false
    }

}

</script>
<style lang="scss" scoped>
</style>